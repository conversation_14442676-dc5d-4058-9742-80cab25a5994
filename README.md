# 旧图集岛个人收藏助手 v1.2

## 新增功能 🆕

### 网络记事本
- **随时记录想法**：在任何页面都可以快速打开记事本
- **云端存储**：笔记自动保存到坚果云 `/notes/` 目录
- **智能命名**：文件按时间戳自动命名（格式：`YYYYMMDD_HHMMSS_标题.txt`）
- **便捷管理**：支持新建、保存、加载笔记

## 功能特点

### 1. 基础收藏功能
- ✅ 一键收藏当前图集
- ✅ 查看收藏列表（支持分页）
- ✅ 删除已收藏内容
- ✅ 点击图片直接打开原页面

### 2. 坚果云同步
- ✅ 使用WebDAV自动同步
- ✅ 每30分钟自动同步一次
- ✅ 支持手动触发同步
- ✅ 多设备数据自动合并
- ✅ 版本控制和备份管理

### 3. 网络记事本 🆕
- ✅ 快速记录想法和笔记
- ✅ 自动保存到坚果云
- ✅ 笔记列表按时间排序
- ✅ 支持加载和编辑已有笔记

### 4. 数据保护
- ✅ 本地数据持久化存储
- ✅ 云端数据版本控制
- ✅ 同步冲突智能处理
- ✅ 防止意外数据丢失

## 使用方法

### 安装
1. 安装 [Tampermonkey](https://www.tampermonkey.net/) 浏览器扩展
2. 复制脚本内容并创建新的用户脚本
3. 保存并启用脚本

### 基本操作
1. **收藏图集**：在图集页面点击"收藏"按钮
2. **查看收藏**：点击"Bookmarks"按钮查看已收藏内容
3. **使用记事本**：点击"记事本"按钮打开网络记事本
4. **同步设置**：点击"同步设置"查看同步状态

### 记事本使用
1. 点击"记事本"按钮打开记事本界面
2. 输入笔记标题和内容
3. 点击"保存当前笔记"保存到云端
4. 点击"加载笔记列表"查看已保存的笔记
5. 点击列表中的笔记可以加载到编辑器中编辑

## 技术说明

### 存储结构
```
坚果云根目录/
├── bookmarks/           # 收藏数据
│   ├── bookmarks.json   # 主文件
│   └── history/         # 历史版本
└── notes/              # 记事本文件 🆕
    ├── 20240306_143022_想法.txt
    ├── 20240306_150315_待办事项.txt
    └── ...
```

### 文件格式
- **收藏数据**：JSON格式，包含图集信息和元数据
- **记事本**：纯文本格式，包含标题、创建时间和内容

### 同步机制
- 使用坚果云WebDAV API进行文件同步
- 支持增量同步和冲突解决
- 自动创建备份版本

## 更新历史

### v1.2 (2024-03-06) 🆕
- ✨ 新增网络记事本功能
- ✨ 支持在坚果云创建和管理笔记
- ✨ 笔记自动按时间戳命名
- 🎨 优化UI布局，增加记事本按钮
- 🔧 改进文件类型处理（支持文本文件）

### v1.1 (2024-03-05)
- ✨ 添加坚果云同步功能
- ✨ 添加同步状态显示和日志记录
- 🔧 优化同步逻辑和错误处理
- 🛡️ 添加数据保护机制

### v1.0 (2024-03-04)
- 🎉 初始版本
- ✨ 实现基本收藏功能
- 🎨 添加基础UI界面

## 支持的网站
- `*.yaltuji.com/*`
- `*.sqmuying.com/*`

## 注意事项
- 脚本已预设坚果云账号，无需额外配置
- 记事本功能需要网络连接才能正常使用
- 建议定期查看同步日志确保数据安全
- 如遇到问题，可以通过"同步设置"重置同步状态

## 许可证
本项目仅供学习和个人使用。
