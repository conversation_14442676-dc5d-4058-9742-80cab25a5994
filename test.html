<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 旧图集岛个人收藏助手</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-left: 4px solid #f17c67;
            border-radius: 4px;
        }
        .buttons {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
        }
        .test-btn {
            background: #f17c67;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #e76450;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>旧图集岛个人收藏助手 v1.2</h1>
        
        <div class="info">
            <strong>测试说明：</strong>
            <p>这是一个测试页面，用于验证脚本功能。请确保已安装Tampermonkey并启用了脚本。</p>
            <p>由于这不是真实的图集网站，收藏功能可能不会完全正常工作，但记事本和同步设置功能应该可以正常使用。</p>
        </div>

        <h2>新增功能</h2>
        <ul class="feature-list">
            <li><strong>网络记事本</strong> - 随时记录想法，自动保存到坚果云</li>
            <li><strong>笔记管理</strong> - 支持新建、保存、加载笔记</li>
            <li><strong>云端存储</strong> - 笔记保存在坚果云 /notes/ 目录</li>
            <li><strong>时间戳命名</strong> - 自动按时间戳命名文件</li>
        </ul>

        <h2>原有功能</h2>
        <ul class="feature-list">
            <li><strong>一键收藏</strong> - 收藏当前图集到本地和云端</li>
            <li><strong>收藏管理</strong> - 查看、删除已收藏内容</li>
            <li><strong>坚果云同步</strong> - 多设备数据自动同步</li>
            <li><strong>版本控制</strong> - 云端数据版本控制和备份</li>
        </ul>

        <h2>使用方法</h2>
        <ol>
            <li>点击左上角的"记事本"按钮打开记事本功能</li>
            <li>在记事本中输入标题和内容</li>
            <li>点击"保存当前笔记"将笔记保存到坚果云</li>
            <li>点击"加载笔记列表"查看已保存的笔记</li>
            <li>点击列表中的笔记可以加载到编辑器中</li>
        </ol>

        <div class="info">
            <strong>技术说明：</strong>
            <p>记事本功能使用坚果云WebDAV API，笔记以文本文件形式存储在云端。</p>
            <p>文件命名格式：YYYYMMDD_HHMMSS_标题.txt</p>
            <p>所有笔记都保存在坚果云的 /notes/ 目录下。</p>
        </div>
    </div>

    <!-- 模拟脚本按钮 -->
    <div class="buttons">
        <button class="test-btn" onclick="alert('这是模拟按钮，请使用真实的脚本按钮')">Bookmarks</button>
        <button class="test-btn" onclick="alert('这是模拟按钮，请使用真实的脚本按钮')">记事本</button>
        <button class="test-btn" onclick="alert('这是模拟按钮，请使用真实的脚本按钮')">收藏</button>
    </div>

    <script>
        // 模拟脚本环境
        console.log('测试页面已加载');
        console.log('请确保已安装Tampermonkey并启用了脚本');
        
        // 检查是否有脚本运行
        setTimeout(() => {
            const scriptButtons = document.querySelector('.ya-bookmark-container');
            if (scriptButtons) {
                console.log('✅ 脚本已正常加载');
                document.querySelector('.buttons').style.display = 'none';
            } else {
                console.log('❌ 脚本未加载，请检查Tampermonkey设置');
            }
        }, 1000);
    </script>
</body>
</html>
