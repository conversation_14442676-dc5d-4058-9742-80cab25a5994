// ==UserScript==
// @name         旧图集岛个人收藏助手
// @namespace    http://tampermonkey.net/
// @version      1.2
// @description  支持收藏功能、坚果云同步和网络记事本
// <AUTHOR> name
// @match        *://*.yaltuji.com/*
// @match        *://*.sqmuying.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_registerMenuCommand
// @grant        GM_notification
// @grant        GM_xmlhttpRequest
// @connect      dav.jianguoyun.com
// @connect      jianguoyun.com
// ==/UserScript==

/*
功能说明：
1. 基础功能：
   - 一键收藏当前图集
   - 查看收藏列表
   - 删除已收藏内容
   - 点击图片直接打开原页面

2. 同步功能：
   - 使用坚果云WebDAV自动同步
   - 每30分钟自动同步一次
   - 支持手动触发同步
   - 多设备数据自动合并

3. 记事本功能：
   - 网络记事本，随时记录想法
   - 自动保存到坚果云 /notes/ 目录
   - 支持新建、保存、加载笔记
   - 笔记列表按时间排序显示

4. 数据保护：
   - 本地数据持久化存储
   - 云端数据版本控制
   - 同步冲突智能处理
   - 防止意外数据丢失

更新历史：
v1.2 (2024-03-06)
- 新增网络记事本功能
- 支持在坚果云创建和管理笔记
- 笔记自动按时间戳命名
- 优化UI布局，增加记事本按钮

v1.1 (2024-03-05)
- 添加了同步功能
- 使用坚果云WebDAV服务
- 添加同步状态显示
- 添加同步日志记录
- 优化了同步逻辑和错误处理
- 添加了数据保护机制

v1.0 (2024-03-04)
- 初始版本
- 实现基本的收藏功能
- 支持查看和管理收藏
- 添加基础UI界面

使用说明：
1. 在图集页面点击"收藏"按钮收藏当前图集
2. 点击"我的收藏"查看已收藏内容
3. 点击"记事本"打开网络记事本功能
4. 同步功能已预设坚果云账号，无需配置
5. 点击"同步设置"可查看同步状态和日志
*/

GM_addStyle(`
    /* 已有样式保持不变 */

    /* 手动同步按钮样式 */
    .manual-sync-btns {
        display: flex;
        gap: 10px;
        margin: 10px 0;
    }

    .ya-btn.upload {
        background: #28a745 !important;
    }

    .ya-btn.download {
        background: #17a2b8 !important;
    }

    .ya-btn .arrow {
        font-weight: bold;
        margin-right: 5px;
    }

    /* 修改页面布局相关的样式 */
    .ya-bookmark-page {
        padding: 20px 40px;  /* 增加左右内边距 */
        max-width: 100%;     /* 取消最大宽度限制 */
        margin: 0 auto;
        background: #fff;
        min-height: 100vh;
    }

    .ya-bookmark-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }

    .ya-bookmark-controls {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .ya-bookmark-actions {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .layout-control select,
    .batch-open-control select {
        padding: 4px 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .ya-bookmark-grid {
        display: grid;
        grid-template-columns: repeat(var(--columns, 6), 1fr) !important;
        gap: 15px;
        padding: 10px 0;
        width: 100%;         /* 确保网格占满容器宽度 */
    }

    .ya-bookmark-item {
        position: relative;
        background: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s;
        aspect-ratio: 2/3;   /* 修改为2:3比例，符合原网站比例 */
    }

    .ya-bookmark-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .ya-btn {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        color: white;
        background: #f17c67;
    }

    .ya-btn:hover {
        background: #e76450;
    }

    .clear-btn {
        background: #dc3545;
    }

    .clear-btn:hover {
        background: #c82333;
    }

    .batch-open-control {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .ya-bookmark-item .cover {
        position: relative;
        height: 100%;        /* 让封面图占满整个卡片 */
    }

    .ya-bookmark-item .cover img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover; /* 修改为cover确保图片填满容器并保持比例 */
        background-color: #f5f5f5; /* 添加背景色以便于区分图片边界 */
        max-height: 100%; /* 确保图片不超出容器高度 */
        max-width: 100%; /* 确保图片不超出容器宽度 */
    }

    .ya-bookmark-item .info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 6px 8px; /* 减小内边距 */
        background: linear-gradient(transparent, rgba(0,0,0,0.85));
        color: white;
        opacity: 1;
        transition: opacity 0.3s;
    }

    .ya-bookmark-item:hover .info {
        opacity: 1;
    }

    .ya-bookmark-item .title {
        font-size: 14px;
        margin: 0 0 5px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ya-bookmark-item .title a {
        color: white;
        text-decoration: none;
    }

    .ya-bookmark-item .meta {
        font-size: 12px;
        margin-top: 4px;
    }

    .ya-bookmark-item .bookmark-details {
        display: flex;
        flex-direction: column;
        gap: 2px; /* 从5px减小到2px */
    }

    .ya-bookmark-item .model,
    .ya-bookmark-item .tags,
    .ya-bookmark-item .date {
        font-size: 14px;
        line-height: 1.2; /* 从1.3减小到1.2 */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ya-bookmark-item .model span,
    .ya-bookmark-item .tags span {
        opacity: 0.7;
    }

    .ya-bookmark-item .date {
        opacity: 0.6;
        margin-top: 2px;
        font-size: 11px;
    }

    /* 分页导航样式 */
    .ya-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 30px;
        padding: 15px 0;
        border-top: 1px solid #eee;
        flex-wrap: wrap;
        gap: 10px;
    }

    .ya-pagination-pages {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .ya-pagination-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        padding: 0 10px;
        border-radius: 4px;
        background: #f5f5f5;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
        text-decoration: none;
    }

    .ya-pagination-btn:hover {
        background: #e0e0e0;
    }

    .ya-pagination-btn.active {
        background: #f17c67;
        color: white;
    }

    .ya-pagination-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .ya-pagination-jump {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: 15px;
    }

    .ya-pagination-jump input {
        width: 50px;
        height: 36px;
        padding: 0 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        text-align: center;
    }

    .ya-pagination-jump button {
        height: 36px;
        padding: 0 12px;
    }

    .ya-pagination-ellipsis {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        color: #666;
    }

    /* 移动设备适配 */
    @media screen and (max-width: 768px) {
        .ya-pagination {
            flex-direction: column;
            gap: 15px;
        }

        .ya-pagination-jump {
            margin-left: 0;
        }
    }

    /* 优化小屏幕显示 */
    @media screen and (max-width: 1200px) {
        .ya-bookmark-page {
            padding: 20px;
        }
    }

    /* 优化超宽屏幕显示 */
    @media screen and (min-width: 2000px) {
        .ya-bookmark-page {
            padding: 20px 80px;
        }
    }

    .ya-bookmark-item img {
        will-change: transform;
        contain: paint;
        backface-visibility: hidden;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }
`);

// 添加新样式以支持UI优化
GM_addStyle(`
    /* 加宽布局 */
    .ya-bookmark-page {
        max-width: 95% !important;
        padding: 20px 40px !important;
    }

    /* 导航条悬浮固定在底部 */
    .ya-pagination {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        background: white !important;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1) !important;
        padding: 10px 0 !important;
        z-index: 9998 !important;
        margin-top: 0 !important;
    }

    /* 为底部固定导航腾出空间 */
    .ya-bookmark-page {
        padding-bottom: 70px !important;
    }

    /* Bookmarks按钮移至右上角 */
    .ya-bookmark-container {
        left: auto !important;
        right: 100px !important;
    }

    /* 增大关闭按钮 */
    .ya-close-btn {
        font-size: 36px !important;
        height: 40px !important;
        width: 40px !important;
        line-height: 40px !important;
        text-align: center !important;
        top: 5px !important;
        right: 5px !important;
    }

    /* 页码显示更多 */
    .ya-pagination-pages {
        display: flex !important;
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 5px !important;
        max-width: 80% !important;
        margin: 0 auto !important;
    }

    .ya-pagination-btn {
        min-width: 36px !important;
    }

    .ya-pagination-jump input {
        width: 60px !important;
    }
`);

(() => {
    'use strict';

    // 修改样式 - 主要是按钮位置的变化
    GM_addStyle(`
        .ya-bookmark-container {
            position: fixed;
            left: 20px;
            top: 20px;
            z-index: 9999;
            display: flex;
            gap: 10px;
        }

        /* 记事本相关样式 */
        .ya-notepad-dialog {
            width: 800px;
            max-width: 90vw;
            max-height: 90vh;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
            overflow: auto;
            padding: 20px;
        }

        .ya-notepad-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .ya-notepad-form h3 {
            margin: 0 0 20px 0;
            text-align: center;
            color: #333;
        }

        .ya-notepad-form textarea {
            width: 100%;
            min-height: 300px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
        }

        .ya-notepad-form .form-btns {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .ya-notepad-form .sync-info {
            margin-top: 15px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        .ya-notepad-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            margin: 10px 0;
        }

        .ya-notepad-item {
            padding: 8px 12px;
            border-bottom: 1px solid #f5f5f5;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .ya-notepad-item:hover {
            background-color: #f8f9fa;
        }

        .ya-notepad-item:last-child {
            border-bottom: none;
        }

        .ya-notepad-item .note-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .ya-notepad-item .note-preview {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .ya-notepad-item .note-time {
            font-size: 11px;
            color: #999;
            margin-top: 4px;
        }
        .ya-btn {
            background: #f17c67;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        .ya-btn:hover {
            background: #e76450;
        }
        .ya-bookmark-list {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.2);
            max-height: 90vh;
            width: 90vw;
            overflow-y: auto;
            z-index: 10000;
        }
        .ya-bookmark-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 20px;
            padding: 20px 0;
        }
        .ya-bookmark-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: #f5f5f5;
            transition: all 0.3s;
        }
        .ya-bookmark-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .ya-bookmark-item .cover {
            position: relative;
            width: 100%;
            height: 100%; /* 使用高度100%代替padding-top */
            overflow: hidden;
        }
        .ya-bookmark-item .cover img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover; /* 修改为cover确保图片填满容器 */
            background-color: #f5f5f5; /* 添加背景色以便于区分图片边界 */
            display: block; /* 确保图片作为块元素 */
        }
        .ya-bookmark-item .info {
            padding: 6px 8px; /* 减小内边距 */
            opacity: 1; /* 确保标题始终显示 */
            background: linear-gradient(transparent, rgba(0,0,0,0.85));
            z-index: 2;
        }
        .ya-bookmark-item .title {
            font-size: 14px;
            margin: 0 0 5px 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .ya-bookmark-item .meta {
            margin-top: 4px;
            font-size: 12px;
        }

        .ya-bookmark-item .bookmark-details {
            display: flex;
            flex-direction: column;
            gap: 2px; /* 从5px减小到2px */
        }

        .ya-bookmark-item .model,
        .ya-bookmark-item .tags {
            font-size: 13px; /* 字体从14px减小到13px */
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.7);
        }

        .ya-bookmark-item .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 1px; /* 从2px进一步减小到1px */
            white-space: normal;
        }

        .ya-bookmark-item .model a,
        .ya-bookmark-item .tags a {
            color: #ffd700;
            text-decoration: none;
            transition: color 0.2s, transform 0.2s;
            display: inline-block;
            padding: 0px 3px;
            margin-right: 2px;
            margin-bottom: 2px;
            border-radius: 2px;
            background-color: rgba(0,0,0,0.3);
        }

        .ya-bookmark-item .model a:hover,
        .ya-bookmark-item .tags a:hover {
            color: #ffffff;
            background-color: rgba(0,0,0,0.5);
            transform: translateY(-1px);
            text-decoration: none;
        }

        .ya-bookmark-item .actions {
            position: absolute;
            top: 5px;
            right: 5px;
            display: none;
        }
        .ya-bookmark-item:hover .actions {
            display: flex;
            gap: 5px;
        }
        .ya-bookmark-item .actions button {
            padding: 5px 10px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .ya-bookmark-item .actions button:hover {
            background: rgba(0,0,0,0.7);
        }
        .ya-close-btn {
            position: absolute;
            right: 10px;
            top: 10px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #333;
        }
        .ya-mask {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
        }
        /* 配置对话框样式 */
        .ya-config-dialog {
            width: 800px;  // 增加宽度
            max-width: 90vw;
            max-height: 90vh;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
            overflow: auto;
            padding: 20px;
        }
        .ya-config-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }
        .backup-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .backup-item {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #eee;
            position: relative;  /* 添加相对定位 */
        }
        .backup-info {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
            padding-right: 80px;  /* 为按钮留出空间 */
        }
        .backup-info > div:first-child {
            color: #333;
            font-weight: bold;
        }
        .backup-actions {
            position: absolute;  /* 绝对定位 */
            top: 8px;           /* 对齐到顶部 */
            right: 8px;         /* 对齐到右侧 */
        }
        .rollback-btn {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            white-space: nowrap;
        }
        .rollback-btn:hover {
            background: #ff5252;
        }
        .backup-list {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin: 10px 0;
        }
        .ya-config-form h3 {
            margin: 0 0 20px 0;
            text-align: center;
        }
        .ya-config-form .form-item {
            margin-bottom: 15px;
        }
        .ya-config-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .ya-config-form input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .ya-config-form .form-btns {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        .ya-config-form .sync-info {
            margin-top: 15px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .ya-config-form .form-tips {
            margin-bottom: 15px;
        }
        .ya-config-form .form-tips p {
            margin: 5px 0;
        }
        .ya-config-form .form-tips a {
            color: #007bff;
            text-decoration: none;
        }
        .ya-btn {
            position: relative;
            overflow: hidden;
        }
        .ya-btn:active {
            transform: scale(0.98);
        }
        .ya-btn:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, .5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        .ya-btn:active:after {
            animation: ripple 0.4s ease-out;
        }
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }
        .sync-log {
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }

        .sync-log h4 {
            margin: 0 0 8px 0;
            color: #666;
            font-size: 13px;
        }

        .log-content {
            max-height: 150px;  // 减小高度
            overflow-y: auto;
            font-size: 12px;
        }

        .log-item {
            padding: 3px 8px;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            align-items: center;
        }

        .log-item .time {
            color: #999;
            margin-right: 8px;
            font-size: 11px;
            white-space: nowrap;
        }

        .log-item .message {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .log-item.success .message {
            color: #28a745;
        }

        .log-item.error .message {
            color: #dc3545;
        }

        .log-item.info .message {
            color: #17a2b8;
        }
    `);

    GM_addStyle(`
        /* 已有样式保持不变 */
        // ... existing code ...
        /* 同步状态和同步设置字体颜色加深，去除opacity */
        .ya-config-form .sync-status-panel,
        .ya-config-form .sync-status-panel .status-item,
        .ya-config-form .sync-status-panel .label,
        .ya-config-form .sync-status-panel .value,
        .ya-config-form .sync-settings,
        .ya-config-form .sync-settings h4,
        .ya-config-form .sync-settings label,
        .ya-config-form .sync-settings .setting-desc {
            color: #222 !important;
            opacity: 1 !important;
        }
    `);

    GM_addStyle(`
        /* ...已有样式... */
        .ya-config-form .sync-settings .setting-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .ya-config-form .sync-settings label {
            margin: 0;
            font-weight: 500;
            color: #222 !important;
            opacity: 1 !important;
            white-space: nowrap;
            flex-shrink: 0;
        }
        .ya-config-form .sync-settings .setting-desc {
            flex-basis: 100%;
            font-size: 12px;
            color: #888;
            margin-left: 0;
            margin-top: 2px;
            font-weight: normal;
        }
    `);

    // 添加更强力的全局样式修复
    GM_addStyle(`
        /* 强制所有同步相关区域文字为黑色 */
        .ya-config-form * {
            color: #000 !important;
            opacity: 1 !important;
            text-shadow: none !important;
        }

        /* 特别针对同步状态和设置区域 */
        .ya-config-form .sync-status-panel,
        .ya-config-form .sync-status-panel *,
        .ya-config-form .sync-settings,
        .ya-config-form .sync-settings * {
            color: #000 !important;
            opacity: 1 !important;
            font-weight: normal !important;
            text-shadow: none !important;
        }

        /* 同步设置描述文字稍微浅一点，增加层次感 */
        .ya-config-form .sync-settings .setting-desc {
            color: #444 !important;
        }
    `);

    class WebDAVSync {
        constructor() {
            this.config = this.loadConfig();
            this.MAIN_FILE = 'bookmarks/bookmarks.json';  // 主文件固定路径
            this.BACKUP_DIR = 'bookmarks/history';        // 历史文件目录
            this.AUTO_BACKUP = false;  // 默认关闭自动备份
            this.AUTO_SYNC = false;    // 默认关闭自动同步
        }

        loadConfig() {
            this.AUTO_BACKUP = GM_getValue('auto_backup_enabled', false);  // 默认关闭
            this.AUTO_SYNC = GM_getValue('auto_sync_enabled', false);      // 默认关闭
            return {
                server: 'https://dav.jianguoyun.com/dav',
                username: '<EMAIL>',
                password: 'asu9747wedhvcigy',
                lastSync: GM_getValue('webdav_last_sync', null)
            };
        }

        saveConfig(config) {
            GM_setValue('webdav_server', config.server);
            GM_setValue('webdav_username', config.username);
            GM_setValue('webdav_password', config.password);
            this.config = config;
        }

        createDialog(content) {
            const mask = document.createElement('div');
            mask.className = 'ya-mask';

            const dialog = document.createElement('div');
            dialog.className = 'ya-config-dialog';
            dialog.innerHTML = `
                <button class="ya-close-btn">×</button>
                ${content}
            `;

            // 添加关闭按钮事件
            dialog.querySelector('.ya-close-btn').onclick = () => {
                mask.remove();
                dialog.remove();
            };

            document.body.appendChild(mask);
            return dialog;
        }

        showConfigDialog() {
            // 先渲染载入中
            const html = `
                <div class="ya-config-form">
                    <h3>坚果云同步状态</h3>
                    <!-- 同步状态面板 -->
                    <div class="sync-status-panel">
                        <div class="status-item">
                            <span class="label">同步状态:</span>
                            <span class="value ${this.config.lastSync ? 'success' : 'warning'}">
                                ${this.config.lastSync ? '已连接' : '未连接'}
                            </span>
                        </div>
                        <div class="status-item">
                            <span class="label">本地收藏:</span>
                            <span class="value">${window.yaBookmarkManager.bookmarks.length} 个</span>
                        </div>
                        <div class="status-item">
                            <span class="label">上次同步:</span>
                            <span class="value">
                                ${this.config.lastSync ? new Date(this.config.lastSync).toLocaleString() : '从未同步'}
                            </span>
                        </div>
                        <div class="status-item">
                            <span class="label">同步周期:</span>
                            <span class="value">每30分钟自动同步一次</span>
                        </div>
                        <div class="status-item">
                            <span class="label">坚果云账号:</span>
                            <span class="value"><EMAIL></span>
                        </div>
                        <div class="status-item">
                            <span class="label">云端地址:</span>
                            <span class="value">
                                <a href="https://www.jianguoyun.com/#/safety" target="_blank">查看坚果云文件</a>
                            </span>
                        </div>
                    </div>
                    <!-- 添加同步设置 -->
                    <div class="sync-settings">
                        <h4>同步设置</h4>
                        <div class="setting-item">
                            <label for="auto_sync">
                                启用自动同步
                            </label>
                            <input type="checkbox" id="auto_sync" ${GM_getValue('auto_sync_enabled', false) ? 'checked' : ''}>
                        </div>
                        <div class="setting-desc">勾选后，每30分钟会自动将本地收藏与云端坚果云同步，保持多设备一致。每次同步都会自动创建历史备份，方便回滚。</div>
                    </div>
                    <!-- 修改操作按钮部分 -->
                    <div class="form-btns">
                        <button class="ya-btn primary" id="test_webdav">测试连接</button>
                        <button class="ya-btn warning" id="force_sync">立即同步</button>
                        <div class="manual-sync-btns">
                            <button class="ya-btn upload" id="force_upload">
                                <span class="arrow">↑</span> 上传到云端
                            </button>
                            <button class="ya-btn download" id="force_download">
                                <span class="arrow">↓</span> 从云端下载
                            </button>
                        </div>
                        <button class="ya-btn danger" id="clear_sync">重置同步</button>
                    </div>
                    <!-- 同步日志 -->
                    <div class="sync-log">
                        <h4>同步记录 (最近10条)</h4>
                        <div class="log-content">
                            ${this.getSyncLogs()
                                .slice(0, 10)
                                .map(log => `
                                    <div class="log-item ${log.type}">
                                        <span class="time">${new Date(log.time).toLocaleString()}</span>
                                        <span class="message">${log.message}</span>
                                    </div>
                                `).join('')
                                || '暂无同步记录'
                            }
                        </div>
                    </div>
                    <!-- 备份管理 -->
                    <div class="backup-manager">
                        <h4>备份管理</h4>
                        <div class="backup-list" id="backup-list-loading">
                            <div style="color:#888;text-align:center;padding:20px 0;">载入中...</div>
                        </div>
                        <div style="text-align:center;margin-top:10px;">
                            <button class="ya-btn" id="load-more-backup" style="display:none;">加载更多</button>
                        </div>
                    </div>
                </div>
            `;

            // 在对话框创建后，立即应用内联样式
            const dialog = this.createDialog(html);
            document.body.appendChild(dialog);

            // 强制应用内联样式到所有同步相关元素
            const applyDarkTextStyles = () => {
                const statusItems = dialog.querySelectorAll('.sync-status-panel *');
                const settingItems = dialog.querySelectorAll('.sync-settings *');

                const allItems = [...statusItems, ...settingItems];
                allItems.forEach(el => {
                    el.style.setProperty('color', '#000', 'important');
                    el.style.setProperty('opacity', '1', 'important');
                    el.style.setProperty('text-shadow', 'none', 'important');
                });

                // 描述文字稍淡一点
                const descItems = dialog.querySelectorAll('.setting-desc');
                descItems.forEach(el => {
                    el.style.setProperty('color', '#444', 'important');
                });
            };

            // 执行应用样式
            setTimeout(applyDarkTextStyles, 0);

            // 绑定事件（原有代码不变）
            dialog.querySelector('#test_webdav').onclick = () => this.testConnection(dialog);
            dialog.querySelector('#force_sync').onclick = () => this.forceSync();
            dialog.querySelector('#force_upload').onclick = () => this.forceUploadWithConfirm();
            dialog.querySelector('#force_download').onclick = () => this.forceDownloadWithConfirm();
            dialog.querySelector('#clear_sync').onclick = () => this.clearConfig(dialog);
            // 绑定自动同步开关事件
            const autoSyncCheckbox = dialog.querySelector('#auto_sync');
            if (autoSyncCheckbox) {
                autoSyncCheckbox.onchange = (e) => {
                    this.AUTO_SYNC = e.target.checked;
                    GM_setValue('auto_sync_enabled', this.AUTO_SYNC);
                    GM_notification({
                        text: `自动同步已${this.AUTO_SYNC ? '开启' : '关闭'}`,
                        timeout: 2000
                    });
                };
            }
            // 绑定自动备份开关事件
            const autoBackupCheckbox = dialog.querySelector('#auto_backup');
            if (autoBackupCheckbox) {
                autoBackupCheckbox.onchange = (e) => {
                    this.AUTO_BACKUP = e.target.checked;
                    GM_setValue('auto_backup_enabled', this.AUTO_BACKUP);
                    GM_notification({
                        text: `自动备份已${this.AUTO_BACKUP ? '开启' : '关闭'}`,
                        timeout: 2000
                    });
                };
            }
            // 备份管理加载逻辑
            let allBackups = [];  // 所有已加载的备份
            let nextBackupOffset = 0; // 下一批备份的起始位置
            const backupPageSize = 2; // 每次加载2个
            const backupListDiv = dialog.querySelector('.backup-list');
            const loadMoreBtn = dialog.querySelector('#load-more-backup');

            // 渲染备份函数
            const renderBackups = (backups) => {
                if (!backups || backups.length === 0) {
                    if (allBackups.length === 0) {
                        backupListDiv.innerHTML = '<div style="color:#888;text-align:center;padding:20px 0;">暂无备份</div>';
                    }
                    loadMoreBtn.style.display = 'none';
                    return;
                }

                // 构建备份HTML
                const backupsHtml = backups.map(backup => `
                    <div class="backup-item">
                        <div class="backup-actions">
                            <button class="rollback-btn" data-version="${backup.name}">回滚</button>
                        </div>
                        <div class="backup-info">
                            <div>${new Date(backup.date).toLocaleString()}</div>
                            <div>${backup.bookmarkCount}个收藏</div>
                            <div>${backup.size}</div>
                        </div>
                    </div>
                `).join('');

                // 首次加载或正在加载时替换整个内容
                if (allBackups.length === backups.length ||
                    backupListDiv.textContent.includes('载入中')) {
                    backupListDiv.innerHTML = backupsHtml;
                } else {
                    // 否则追加到现有内容
                    backupListDiv.innerHTML += backupsHtml;
                }

                // 绑定回滚按钮事件
                backupListDiv.querySelectorAll('.rollback-btn').forEach(btn => {
                    // 检查是否已经绑定过事件，避免重复绑定
                    if (!btn.hasEventListener) {
                        btn.hasEventListener = true;
                        btn.onclick = async (e) => {
                            const version = e.target.getAttribute('data-version');
                            if (!version) return;
                            try {
                                e.target.textContent = '正在回滚...';
                                e.target.disabled = true;
                                await this.rollbackToVersion(version);
                                e.target.textContent = '回滚成功';
                                e.target.style.background = '#28a745';
                                setTimeout(() => {
                                    document.querySelectorAll('.ya-config-dialog').forEach(d => d.remove());
                                    document.querySelectorAll('.ya-mask').forEach(m => m.remove());
                                }, 2000);
                            } catch (error) {
                                e.target.textContent = '回滚到此版本';
                                e.target.style.background = '#ff6b6b';
                            }
                        };
                    }
                });

                // 更新下一批的偏移量
                nextBackupOffset += backups.length;

                // 根据是否有更多备份决定显示按钮
                if (backups.length < backupPageSize) {
                    loadMoreBtn.style.display = 'none'; // 没有更多了
                } else {
                    loadMoreBtn.style.display = '';
                    loadMoreBtn.textContent = '加载更多';
                    loadMoreBtn.disabled = false;
                }
            };

            // 加载初始备份（只加载2个）
            this.getBackupList(backupPageSize, 0).then(backups => {
                allBackups = [...backups];
                renderBackups(backups);
            });

            // 加载更多按钮事件
            loadMoreBtn.onclick = () => {
                loadMoreBtn.textContent = '加载中...';
                loadMoreBtn.disabled = true;

                this.getBackupList(backupPageSize, nextBackupOffset).then(newBackups => {
                    // 添加到已加载的备份列表
                    allBackups = [...allBackups, ...newBackups];

                    // 渲染新备份，追加到现有列表
                    renderBackups(newBackups);
                }).catch(error => {
                    console.error('加载更多备份失败:', error);
                    loadMoreBtn.textContent = '加载更多';
                    loadMoreBtn.disabled = false;
                });
            };
        }

        // 显示记事本对话框
        showNotepadDialog() {
            const html = `
                <div class="ya-notepad-form">
                    <h3>网络记事本</h3>
                    <div class="notepad-controls">
                        <button class="ya-btn" id="new-note">新建笔记</button>
                        <button class="ya-btn" id="load-notes">加载笔记列表</button>
                        <button class="ya-btn" id="save-note">保存当前笔记</button>
                    </div>
                    <div class="form-item">
                        <label for="note-title">笔记标题:</label>
                        <input type="text" id="note-title" placeholder="输入笔记标题..." />
                    </div>
                    <div class="form-item">
                        <label for="note-content">笔记内容:</label>
                        <textarea id="note-content" placeholder="在这里输入您的笔记内容..."></textarea>
                    </div>
                    <div class="notepad-list-container">
                        <h4>已保存的笔记:</h4>
                        <div class="ya-notepad-list" id="notes-list">
                            <div style="color:#888;text-align:center;padding:20px 0;">点击"加载笔记列表"查看已保存的笔记</div>
                        </div>
                    </div>
                    <div class="form-btns">
                        <button class="ya-btn" onclick="this.closest('.ya-notepad-dialog').remove();document.querySelector('.ya-mask').remove();">关闭</button>
                    </div>
                    <div class="sync-info">
                        <p>笔记将自动保存到坚果云 /notes/ 目录下</p>
                        <p>文件名格式: YYYYMMDD_HHMMSS_标题.txt</p>
                    </div>
                </div>
            `;

            const dialog = document.createElement('div');
            dialog.className = 'ya-notepad-dialog';
            dialog.innerHTML = `
                <button class="ya-close-btn">×</button>
                ${html}
            `;

            const mask = document.createElement('div');
            mask.className = 'ya-mask';

            // 添加关闭按钮事件
            dialog.querySelector('.ya-close-btn').onclick = () => {
                mask.remove();
                dialog.remove();
            };

            // 绑定按钮事件
            dialog.querySelector('#new-note').onclick = () => this.newNote(dialog);
            dialog.querySelector('#load-notes').onclick = () => this.loadNotesList(dialog);
            dialog.querySelector('#save-note').onclick = () => this.saveNote(dialog);

            document.body.appendChild(mask);
            document.body.appendChild(dialog);

            // 自动加载笔记列表
            this.loadNotesList(dialog);
        }

        // 新建笔记
        newNote(dialog) {
            dialog.querySelector('#note-title').value = '';
            dialog.querySelector('#note-content').value = '';
            GM_notification({ text: '已清空编辑器，可以开始新建笔记', timeout: 2000 });
        }

        // 保存笔记
        async saveNote(dialog) {
            const title = dialog.querySelector('#note-title').value.trim();
            const content = dialog.querySelector('#note-content').value.trim();

            if (!title) {
                GM_notification({ text: '请输入笔记标题', timeout: 2000 });
                return;
            }

            if (!content) {
                GM_notification({ text: '请输入笔记内容', timeout: 2000 });
                return;
            }

            try {
                const timestamp = this.getTimeStampString();
                const filename = `${timestamp}_${title.replace(/[^\w\u4e00-\u9fa5]/g, '_')}.txt`;
                const noteData = `标题: ${title}\n创建时间: ${new Date().toLocaleString()}\n\n${content}`;

                // 确保notes目录存在
                await this.createDirectory('notes');

                // 保存笔记文件
                await this.putFile(`notes/${filename}`, noteData);

                GM_notification({ text: '笔记保存成功！', timeout: 2000 });

                // 重新加载笔记列表
                this.loadNotesList(dialog);

            } catch (error) {
                console.error('保存笔记失败:', error);
                GM_notification({ text: '保存笔记失败: ' + error.message, timeout: 3000 });
            }
        }

        // 加载笔记列表
        async loadNotesList(dialog) {
            const notesList = dialog.querySelector('#notes-list');
            notesList.innerHTML = '<div style="color:#888;text-align:center;padding:20px 0;">加载中...</div>';

            try {
                // 确保notes目录存在
                await this.createDirectory('notes');

                const files = await this.listFiles('notes');
                const noteFiles = files.filter(file => file.name.endsWith('.txt'));

                if (noteFiles.length === 0) {
                    notesList.innerHTML = '<div style="color:#888;text-align:center;padding:20px 0;">暂无保存的笔记</div>';
                    return;
                }

                // 按修改时间排序（最新的在前）
                noteFiles.sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified));

                const notesHtml = noteFiles.map(file => {
                    const titleMatch = file.name.match(/\d{8}_\d{6}_(.+)\.txt$/);
                    const displayTitle = titleMatch ? titleMatch[1].replace(/_/g, ' ') : file.name;

                    return `
                        <div class="ya-notepad-item" data-filename="${file.name}">
                            <div class="note-title">${displayTitle}</div>
                            <div class="note-preview">点击查看内容...</div>
                            <div class="note-time">${new Date(file.lastModified).toLocaleString()}</div>
                        </div>
                    `;
                }).join('');

                notesList.innerHTML = notesHtml;

                // 绑定点击事件
                notesList.querySelectorAll('.ya-notepad-item').forEach(item => {
                    item.onclick = () => this.loadNote(dialog, item.getAttribute('data-filename'));
                });

            } catch (error) {
                console.error('加载笔记列表失败:', error);
                notesList.innerHTML = '<div style="color:#f00;text-align:center;padding:20px 0;">加载失败: ' + error.message + '</div>';
            }
        }

        // 加载指定笔记
        async loadNote(dialog, filename) {
            try {
                const content = await this.getFile(`notes/${filename}`);
                if (!content) {
                    GM_notification({ text: '无法读取笔记内容', timeout: 2000 });
                    return;
                }

                // 解析笔记内容
                const lines = content.split('\n');
                let title = '';
                let noteContent = '';
                let contentStartIndex = 0;

                // 查找标题
                for (let i = 0; i < lines.length; i++) {
                    if (lines[i].startsWith('标题: ')) {
                        title = lines[i].substring(3).trim();
                        contentStartIndex = i + 3; // 跳过标题、时间和空行
                        break;
                    }
                }

                // 获取笔记内容
                if (contentStartIndex < lines.length) {
                    noteContent = lines.slice(contentStartIndex).join('\n');
                }

                // 填充到编辑器
                dialog.querySelector('#note-title').value = title;
                dialog.querySelector('#note-content').value = noteContent;

                GM_notification({ text: '笔记已加载到编辑器', timeout: 2000 });

            } catch (error) {
                console.error('加载笔记失败:', error);
                GM_notification({ text: '加载笔记失败: ' + error.message, timeout: 3000 });
            }
        }

        async testConnection(dialog) {
            try {
                GM_notification({ text: '正在测试连接...', timeout: 2000 });
                const response = await this.propfind(
                    this.config.server,
                    this.config.username,
                    this.config.password
                );

                this.addSyncLog('success', '坚果云连接测试成功');

                const successDialog = this.createDialog(`
                    <div class="ya-config-form">
                        <h3>连接测试成功</h3>
                        <div class="test-result success">
                            <p>✅ 服务器连接正常</p>
                            <p>✅ 账号验证通过</p>
                            <p>✅ WebDAV 服务可用</p>
                        </div>
                        <div class="form-btns">
                            <button class="ya-btn success" onclick="this.closest('.ya-config-dialog').remove();document.querySelector('.ya-mask').remove();">确定</button>
                        </div>
                    </div>
                `);
                document.body.appendChild(successDialog);
            } catch (error) {
                console.error('测试连接失败:', error);

                // 添加测试失败日志
                this.addSyncLog('error', `连接测试失败: ${error.message}`);

                // 显示更详细的错误信息
                const errorDialog = this.createDialog(`
                    <div class="ya-config-form">
                        <h3>连接测试失败</h3>
                        <div class="test-result error">
                            <p>❌ ${this.getErrorMessage(error)}</p>
                            <div class="error-tips">
                                <p>可能的原因：</p>
                                <ul>
                                    <li>账号或密码错误</li>
                                    <li>应用密码已过期</li>
                                    <li>网络连接问题</li>
                                    <li>WebDAV 服务未开启</li>
                                </ul>
                            </div>
                        </div>
                        <div class="form-btns">
                            <button class="ya-btn danger" onclick="this.closest('.ya-config-dialog').remove();document.querySelector('.ya-mask').remove();">关闭</button>
                        </div>
                    </div>
                `);
                document.body.appendChild(errorDialog);
            }
        }

        getErrorMessage(error) {
            if (error.message.includes('401')) {
                return '账号或密码错误';
            } else if (error.message.includes('404')) {
                return '无法访问 WebDAV 服务';
            } else if (error.message.includes('timeout')) {
                return '连接超时';
            }
            return error.message;
        }

        saveSettings(dialog) {
            const config = {
                server: dialog.querySelector('#webdav_server').value,
                username: dialog.querySelector('#webdav_username').value,
                password: dialog.querySelector('#webdav_password').value
            };
            this.saveConfig(config);

            // 显示保存成功提示
            GM_notification({ text: '设置已保存，正在进行首次同步...', timeout: 2000 });

            // 立即进行一次同步
            this.sync(window.yaBookmarkManager.bookmarks)
                .then(() => {
                    this.addSyncLog('success', '首次同步成功');
                    GM_notification({ text: '首次同步完成！', timeout: 2000 });
                    dialog.remove();
                })
                .catch(error => {
                    this.addSyncLog('error', `首次同步失败: ${error.message}`);
                    GM_notification({ text: '首次同步失败，请检查配置', timeout: 3000 });
                    dialog.remove();
                });
        }

        async sync(bookmarks) {
            try {
                const remoteData = await this.getFile(this.MAIN_FILE);

                if (remoteData) {
                    const remoteJson = JSON.parse(remoteData);
                    const remoteBookmarks = remoteJson.bookmarks;

                    // 安全检查：如果本地书签数量明显少于远程，需要确认
                    if (bookmarks.length < remoteBookmarks.length) {
                        return new Promise((resolve, reject) => {
                            const confirmDialog = this.createDialog(`
                                <div class="ya-config-form">
                                    <h3>⚠️ 同步警告</h3>
                                    <div class="test-result warning">
                                        <p>检测到本地收藏(${bookmarks.length}个)少于云端(${remoteBookmarks.length}个)</p>
                                        <p>这可能是由于：</p>
                                        <ul>
                                            <li>本地数据被清空</li>
                                            <li>这是一个新的设备</li>
                                            <li>浏览器数据被清理</li>
                                        </ul>
                                        <p>请选择操作：</p>
                                    </div>
                                    <div class="form-btns">
                                        <button class="ya-btn download" id="use_cloud">
                                            使用云端数据 (${remoteBookmarks.length}个收藏)
                                        </button>
                                        <button class="ya-btn upload" id="use_local">
                                            使用本地数据 (${bookmarks.length}个收藏)
                                        </button>
                                        <button class="ya-btn merge" id="merge_data">
                                            合并数据 (保留双方数据)
                                        </button>
                                        <button class="ya-btn" id="cancel_sync">取消同步</button>
                                    </div>
                                </div>
                            `);

                            document.body.appendChild(confirmDialog);

                            // 使用云端数据
                            confirmDialog.querySelector('#use_cloud').onclick = () => {
                                confirmDialog.remove();
                                document.querySelector('.ya-mask')?.remove();
                                this.addSyncLog('info', `使用云端数据: ${remoteBookmarks.length}个收藏`);
                                resolve(remoteBookmarks);
                            };

                            // 使用本地数据（需要二次确认）
                            confirmDialog.querySelector('#use_local').onclick = () => {
                                if (confirm(`⚠️ 危险操作！确定要用${bookmarks.length}个本地收藏覆盖${remoteBookmarks.length}个云端收藏吗？`)) {
                                    if (confirm('再次确认：这将删除云端额外的收藏！')) {
                                        confirmDialog.remove();
                                        document.querySelector('.ya-mask')?.remove();
                                        this.addSyncLog('warning', `使用本地数据覆盖云端: ${bookmarks.length}个收藏`);
                                        this.uploadToCloud(bookmarks);
                                        resolve(bookmarks);
                                    }
                                }
                            };

                            // 合并数据
                            confirmDialog.querySelector('#merge_data').onclick = () => {
                                confirmDialog.remove();
                                document.querySelector('.ya-mask')?.remove();
                                const mergedBookmarks = this.mergeBookmarks(bookmarks, remoteBookmarks);
                                this.addSyncLog('info', `合并数据: ${mergedBookmarks.length}个收藏`);
                                this.uploadToCloud(mergedBookmarks);
                                resolve(mergedBookmarks);
                            };

                            // 取消同步
                            confirmDialog.querySelector('#cancel_sync').onclick = () => {
                                confirmDialog.remove();
                                document.querySelector('.ya-mask')?.remove();
                                reject(new Error('用户取消同步'));
                            };
                        });
                    }

                    // 正常同步逻辑（本地数据量正常）
                    return this.mergeBookmarks(bookmarks, remoteBookmarks);
                }

                // 首次同步，直接上传本地数据
                await this.uploadToCloud(bookmarks);
                return bookmarks;

            } catch (error) {
                console.error('同步失败:', error);
                throw error;
            }
        }

        // 上传到云端
        async uploadToCloud(bookmarks) {
            const currentVersion = GM_getValue('bookmark_version', 0);
            const newVersion = currentVersion + 1;

            const data = {
                version: newVersion,
                updateTime: new Date().toISOString(),
                bookmarks: bookmarks
            };

            await this.putFile('bookmarks.json', JSON.stringify(data));
            GM_setValue('bookmark_version', newVersion);
            GM_setValue('webdav_last_sync', data.updateTime);
        }

        // 智能合并书签
        mergeBookmarks(local, remote) {
            const merged = [];
            const urlMap = new Map();

            // 先处理本地书签
            local.forEach(bookmark => {
                urlMap.set(bookmark.url, {
                    bookmark,
                    source: 'local'
                });
            });

            // 合并远程书签
            remote.forEach(bookmark => {
                const existing = urlMap.get(bookmark.url);
                if (!existing) {
                    // 新增的书签
                    urlMap.set(bookmark.url, {
                        bookmark,
                        source: 'remote'
                    });
                } else if (new Date(bookmark.addTime) > new Date(existing.bookmark.addTime)) {
                    // 远程版本更新
                    urlMap.set(bookmark.url, {
                        bookmark,
                        source: 'remote'
                    });
                }
            });

            // 转换回数组
            for (const {bookmark} of urlMap.values()) {
                merged.push(bookmark);
            }

            // 按添加时间排序
            return merged.sort((a, b) => new Date(b.addTime) - new Date(a.addTime));
        }

        // 修改上传和下载按钮的处理方法
        async forceUpload(bookmarks) {
            try {
                await this.createBackup(bookmarks);
                await this.uploadToCloud(bookmarks);
                this.addSyncLog('success', `强制上传成功: ${bookmarks.length}个书签`);
                return bookmarks;
            } catch (error) {
                this.addSyncLog('error', `强制上传失败: ${error.message}`);
                throw error;
            }
        }

        // 修改文件名格式，精确到秒
        getTimeStampString() {
            const date = new Date();
            return `${date.getFullYear()}${(date.getMonth()+1).toString().padStart(2,'0')}${date.getDate().toString().padStart(2,'0')}_${date.getHours().toString().padStart(2,'0')}${date.getMinutes().toString().padStart(2,'0')}${date.getSeconds().toString().padStart(2,'0')}_${date.getMilliseconds().toString().padStart(3,'0')}`;
        }

        // 修改 forceDownloadWithConfirm 方法，显示最近5个版本供选择
        async forceDownloadWithConfirm() {
            try {
                // 获取最新的5个版本
                const recentFiles = await this.getBackupList();

                if (recentFiles.length === 0) {
                    throw new Error('云端暂无可用版本');
                }

                // 创建选择对话框
                const dialog = this.createDialog(`
                    <div class="ya-config-form">
                        <h3>选择要下载的版本</h3>
                        <div class="version-list">
                            ${recentFiles.map(file => `
                                <div class="version-item">
                                    <div class="version-info">
                                        <span class="version-time">${file.date.toLocaleString()}</span>
                                        <span class="version-count">${file.bookmarkCount}个收藏</span>
                                        <span class="version-size">${file.size}</span>
                                    </div>
                                    <button class="ya-btn download-version" data-file="${file.name}">
                                        下载此版本
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                        <div class="form-btns">
                            <button class="ya-btn" onclick="this.closest('.ya-config-dialog').remove();document.querySelector('.ya-mask').remove();">取消</button>
                        </div>
                    </div>
                `);

                // 绑定下载按钮事件
                dialog.querySelectorAll('.download-version').forEach(btn => {
                    btn.onclick = async () => {
                        try {
                            const filename = btn.getAttribute('data-file');
                            GM_notification({ text: '正在下载选中的版本...', timeout: 2000 });

                            // 读取选中的版本文件
                            const fileData = await this.getFile(`bookmarks/${filename}`);
                            if (!fileData) {
                                throw new Error('无法读取选中的版本');
                            }

                            const data = JSON.parse(fileData);
                            window.yaBookmarkManager.bookmarks = data.bookmarks;
                            window.yaBookmarkManager.saveBookmarks();

                            // 如果当前在收藏页面，刷新显示
                            if (document.querySelector('.ya-bookmark-page')) {
                                window.yaBookmarkManager.showBookmarks();
                            }

                            this.addSyncLog('success', `已下载 ${new Date(data.updateTime).toLocaleString()} 的版本: ${data.bookmarks.length}个收藏`);
                            GM_notification({ text: `下载完成！共${data.bookmarks.length}个收藏`, timeout: 2000 });

                            // 关闭对话框
                            document.querySelectorAll('.ya-config-dialog').forEach(d => d.remove());
                            document.querySelectorAll('.ya-mask').forEach(m => m.remove());

                        } catch (error) {
                            this.addSyncLog('error', `版本下载失败: ${error.message}`);
                            GM_notification({ text: '下载失败: ' + error.message, timeout: 3000 });
                        }
                    };
                });

                document.body.appendChild(dialog);

            } catch (error) {
                this.addSyncLog('error', `获取版本列表失败: ${error.message}`);
                GM_notification({ text: '获取版本列表失败: ' + error.message, timeout: 3000 });
            }
        }

        // WebDAV 基础操作
        propfind(path, depth = 0) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'PROPFIND',
                    url: this.config.server + '/' + path,
                    headers: {
                        'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password),
                        'Depth': depth.toString(),
                        'Content-Type': 'application/xml;charset=UTF-8'
                    },
                    data: '<?xml version="1.0" encoding="utf-8"?><propfind xmlns="DAV:"><prop><getlastmodified/><getcontentlength/><resourcetype/></prop></propfind>',
                    onload: (response) => {
                        if (response.status === 207 || response.status === 200) {
                            resolve(response);
                        } else {
                            reject(new Error(`PROPFIND 失败: ${response.status}`));
                        }
                    },
                    onerror: (error) => reject(error)
                });
            });
        }

        getFile(filename) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: this.config.server + '/' + filename,
                    headers: {
                        'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password)
                    },
                    onload: (response) => {
                        if (response.status === 200) {
                            resolve(response.responseText);
                        } else if (response.status === 404) {
                            resolve(null);
                        } else {
                            reject(new Error('GET failed: ' + response.status));
                        }
                    },
                    onerror: (error) => reject(error)
                });
            });
        }

        async putFile(filename, content) {
            try {
                // 如果是记事本文件，直接保存
                if (filename.startsWith('notes/')) {
                    return await this._putSingleFile(filename, content);
                }

                // 原有的书签文件逻辑
                // 创建主目录
                await this.createDirectory('bookmarks');

                // 生成带时间戳的文件名
                const dateStr = this.getTimeStampString();
                const newFile = `bookmarks/bookmarks_${dateStr}.json`;

                // 保存新文件
                await this._putSingleFile(newFile, content);

                // 同时更新主文件（用于快速访问最新版本）
                await this._putSingleFile(this.MAIN_FILE, content);

                return newFile;  // 返回新文件的路径
            } catch (error) {
                console.error('putFile error:', error);
                throw error;
            }
        }

        async _putSingleFile(filename, content) {
            return new Promise((resolve, reject) => {
                // 根据文件类型设置Content-Type
                const contentType = filename.endsWith('.txt') ? 'text/plain; charset=utf-8' : 'application/json';

                GM_xmlhttpRequest({
                    method: 'PUT',
                    url: this.config.server + '/' + filename,
                    headers: {
                        'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password),
                        'Content-Type': contentType
                    },
                    data: content,
                    onload: (response) => {
                        if (response.status === 201 || response.status === 204) {
                            resolve(response);
                        } else {
                            reject(new Error(`文件上传失败: ${response.status}`));
                        }
                    },
                    onerror: (error) => reject(error)
                });
            });
        }

        async createDirectory(path) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'MKCOL',
                    url: this.config.server + '/' + path,
                    headers: {
                        'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password)
                    },
                    onload: (response) => {
                        // 201 创建成功，405 目录已存在，都算正常
                        if (response.status === 201 || response.status === 405) {
                            resolve(response);
                        } else {
                            reject(new Error(`创建目录失败: ${response.status}`));
                        }
                    },
                    onerror: (error) => reject(error)
                });
            });
        }

        // 列出目录中的文件
        async listFiles(directory) {
            try {
                const response = await this.propfind(directory, 1);
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(response.responseText, 'text/xml');
                const responses = xmlDoc.querySelectorAll('response');

                const files = [];
                responses.forEach(resp => {
                    const href = resp.querySelector('href')?.textContent;
                    const lastModified = resp.querySelector('getlastmodified')?.textContent;
                    const contentLength = resp.querySelector('getcontentlength')?.textContent;
                    const resourceType = resp.querySelector('resourcetype');

                    if (href && !resourceType?.querySelector('collection')) {
                        // 这是一个文件，不是目录
                        const filename = href.split('/').pop();
                        if (filename && filename !== directory) {
                            files.push({
                                name: filename,
                                lastModified: lastModified ? new Date(lastModified) : new Date(),
                                size: contentLength ? this.formatFileSize(parseInt(contentLength)) : '未知大小'
                            });
                        }
                    }
                });

                return files;
            } catch (error) {
                console.error('列出文件失败:', error);
                throw error;
            }
        }

        // 格式化文件大小
        formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 添加同步日志相关方法
        getSyncLogs() {
            return JSON.parse(GM_getValue('sync_logs', '[]'));
        }

        addSyncLog(type, message) {
            const logs = this.getSyncLogs();
            logs.unshift({
                time: new Date().toISOString(),
                type,
                message
            });

            // 只保留最近5条记录
            GM_setValue('sync_logs', JSON.stringify(logs.slice(0, 5)));

            // 如果当前有日志显示，更新它
            const logContent = document.querySelector('.log-content');
            if (logContent) {
                this.updateLogDisplay(logContent);
            }
        }

        updateLogDisplay(container) {
            const logs = JSON.parse(GM_getValue('sync_logs', '[]'));
            container.innerHTML = logs.slice(0, 5).map(log => `
                <div class="log-item ${log.type}">
                    <span class="time">${new Date(log.time).toLocaleString()}</span>
                    <span class="message">${log.message}</span>
                </div>
            `).join('');
        }

        // 清除配置方法
        clearConfig(dialog) {
            if (confirm('确定要重置同步状态吗？这将重新开始同步。')) {
                GM_deleteValue('webdav_last_sync');
                this.config = this.loadConfig();
                this.addSyncLog('info', '同步状态已重置');
                GM_notification({ text: '同步状态已重置', timeout: 2000 });
                dialog.remove();
                this.showConfigDialog();
            }
        }

        // 修改 forceSync 方法
        async forceSync() {
            const startTime = new Date();
            try {
                GM_notification({ text: '正在执行同步...', timeout: 2000 });

                // 添加开始同步日志
                this.addSyncLog('info', '开始执行同步');

                const mergedBookmarks = await this.sync(window.yaBookmarkManager.bookmarks);
                window.yaBookmarkManager.bookmarks = mergedBookmarks;
                window.yaBookmarkManager.saveBookmarks();

                const endTime = new Date();
                const duration = ((endTime - startTime) / 1000).toFixed(1);

                // 添加成功日志
                this.addSyncLog('success', `同步成功 ✅ 耗时${duration}秒，共同步${mergedBookmarks.length}个收藏`);

                // 显示成功通知
                GM_notification({
                    text: `同步完成！共${mergedBookmarks.length}个收藏`,
                    timeout: 2000
                });

                // 自动关闭所有对话框
                document.querySelectorAll('.ya-config-dialog').forEach(dialog => dialog.remove());
                document.querySelectorAll('.ya-mask').forEach(mask => mask.remove());

                // 如果当前在收藏页面，刷新显示
                if (document.querySelector('.ya-bookmark-page')) {
                    window.yaBookmarkManager.showBookmarks();
                }

            } catch (error) {
                console.error('同步失败:', error);
                this.addSyncLog('error', `同步失败 ❌: ${error.message}`);
                GM_notification({ text: '同步失败: ' + error.message, timeout: 3000 });
            }
        }

        // 添加回滚功能
        async rollback(backupDate) {
            try {
                // 获取指定日期的备份文件
                const backupFile = `${this.BACKUP_DIR}/bookmarks_${backupDate}.json`;
                const backupData = await this.getFile(backupFile);

                if (!backupData) {
                    throw new Error('找不到指定日期的备份');
                }

                // 先备份当前数据
                await this.createBackup('pre_rollback');

                // 恢复备份数据
                const data = JSON.parse(backupData);
                await this.uploadToCloud(data.bookmarks);

                this.addSyncLog('info', `已回滚到 ${backupDate} 的备份`);
                return data.bookmarks;
            } catch (error) {
                this.addSyncLog('error', `回滚失败: ${error.message}`);
                throw error;
            }
        }

        // 获取可用的备份列表
        async getBackupList(limit = 5, offset = 0) {
            try {
                // 获取 bookmarks 目录下的所有文件
                const response = await this.propfind('bookmarks', 1);

                // 解析 XML 响应
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(response.responseText, "text/xml");
                const responses = xmlDoc.getElementsByTagName("d:response");

                const files = [];
                for (let i = 0; i < responses.length; i++) {
                    const href = responses[i].getElementsByTagName("d:href")[0]?.textContent;
                    if (!href || href.endsWith('/')) continue; // 跳过目录

                    const lastModified = responses[i].getElementsByTagName("d:getlastmodified")[0]?.textContent;
                    const contentLength = responses[i].getElementsByTagName("d:getcontentlength")[0]?.textContent;

                    // 从文件名中提取信息
                    const fileName = href.split('/').pop();
                    // 只处理 bookmarks_ 开头的文件，跳过主文件
                    if (!fileName.startsWith('bookmarks_')) continue;

                    // 暂时不加载文件内容，减少请求
                    files.push({
                        name: fileName,
                        date: new Date(lastModified),
                        size: this.formatFileSize(contentLength),
                        path: `bookmarks/${fileName}`
                    });
                }

                // 按时间降序排序
                const sortedFiles = files.sort((a, b) => b.date - a.date);

                // 应用分页
                const paginatedFiles = sortedFiles.slice(offset, offset + limit);

                // 只为这一批次加载文件内容
                const filesWithContent = [];
                for (const file of paginatedFiles) {
                    try {
                        const fileContent = await this.getFile(file.path);
                        const data = JSON.parse(fileContent);
                        filesWithContent.push({
                            ...file,
                            bookmarkCount: data.bookmarks.length,
                            updateTime: data.updateTime
                        });
                    } catch (error) {
                        console.error('读取文件失败:', file.name, error);
                    }
                }

                return filesWithContent;
            } catch (error) {
                console.error('获取文件列表失败:', error);
                return [];
            }
        }

        // 添加文件大小格式化方法
        formatFileSize(bytes) {
            if (!bytes) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 添加备份相关样式
        addBackupStyles() {
            GM_addStyle(`
                .backup-manager {
                    margin-top: 20px;
                    border-top: 1px solid #eee;
                    padding-top: 15px;
                }

                .backup-list {
                    max-height: 200px;
                    overflow-y: auto;
                }

                .backup-item {
                    display: flex;
                    align-items: center;
                    padding: 8px;
                    border-bottom: 1px solid #f5f5f5;
                }

                .backup-date {
                    flex: 1;
                    font-size: 12px;
                }

                .backup-size {
                    margin: 0 10px;
                    color: #666;
                    font-size: 11px;
                }

                .ya-btn.small {
                    padding: 3px 8px;
                    font-size: 12px;
                }
            `);
        }

        // 修改 forceUploadWithConfirm 方法
        async forceUploadWithConfirm() {
            try {
                GM_notification({ text: '正在上传到云端...', timeout: 2000 });
                const data = {
                    version: Date.now(),
                    updateTime: new Date().toISOString(),
                    bookmarks: window.yaBookmarkManager.bookmarks
                };

                // 使用完整路径
                await this.putFile(this.MAIN_FILE, JSON.stringify(data));

                this.addSyncLog('success', `上传成功: ${window.yaBookmarkManager.bookmarks.length}个收藏`);
                GM_notification({ text: `上传完成！共${window.yaBookmarkManager.bookmarks.length}个收藏`, timeout: 2000 });

                // 自动关闭对话框
                document.querySelectorAll('.ya-config-dialog').forEach(dialog => dialog.remove());
                document.querySelectorAll('.ya-mask').forEach(mask => mask.remove());

            } catch (error) {
                this.addSyncLog('error', `上传失败: ${error.message}`);
                GM_notification({ text: '上传失败: ' + error.message, timeout: 3000 });
            }
        }

        // 修改回滚按钮的实现
        async rollbackToVersion(version) {
            try {
                GM_notification({ text: '正在回滚到选中版本...', timeout: 2000 });

                // 读取选中的版本文件
                const fileData = await this.getFile(`bookmarks/${version}`);
                if (!fileData) {
                    throw new Error('无法读取选中的版本');
                }

                // 解析并应用数据
                const data = JSON.parse(fileData);
                window.yaBookmarkManager.bookmarks = data.bookmarks;
                window.yaBookmarkManager.saveBookmarks();

                // 如果当前在收藏页面，刷新显示
                if (document.querySelector('.ya-bookmark-page')) {
                    window.yaBookmarkManager.showBookmarks();
                }

                this.addSyncLog('success', `已回滚到 ${new Date(data.updateTime).toLocaleString()} 的版本: ${data.bookmarks.length}个收藏`);
                GM_notification({ text: `回滚完成！共${data.bookmarks.length}个收藏`, timeout: 2000 });

                return true;
            } catch (error) {
                this.addSyncLog('error', `回滚失败: ${error.message}`);
                GM_notification({ text: '回滚失败: ' + error.message, timeout: 3000 });
                throw error;
            }
        }

        // 修改备份列表的显示
        async showBackupList() {
            try {
                const backups = await this.getBackupList();
                const recentBackups = backups.slice(0, 5); // 只显示最近5个

                return `
                    <div class="backup-list">
                        ${recentBackups.map(backup => `
                            <div class="backup-item">
                                <div class="backup-actions">
                                    <button class="rollback-btn" data-version="${backup.name}">
                                        回滚
                                    </button>
                                </div>
                                <div class="backup-info">
                                    <div>${new Date(backup.date).toLocaleString()}</div>
                                    <div>${backup.bookmarkCount}个收藏</div>
                                    <div>${backup.size}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } catch (error) {
                return `<div class="error">获取备份列表失败: ${error.message}</div>`;
            }
        }
    }

    class YaBookmarkManager {
        constructor() {
            this.webdav = new WebDAVSync();
            this.bookmarks = this.loadBookmarks();
            this.initUI();

            // 添加同步设置按钮
            GM_registerMenuCommand('同步设置', () => this.webdav.showConfigDialog());

            // 定期自动同步
            setInterval(() => this.autoSync(), 30 * 60 * 1000); // 每30分钟
        }

        loadBookmarks() {
            return JSON.parse(GM_getValue('ya_bookmarks', '[]'));
        }

        saveBookmarks() {
            GM_setValue('ya_bookmarks', JSON.stringify(this.bookmarks));
        }

        initUI() {
            // 创建容器
            const container = document.createElement('div');
            container.className = 'ya-bookmark-container';

            // 创建我的收藏按钮
            const myBookmarksBtn = document.createElement('button');
            myBookmarksBtn.className = 'ya-btn ya-list-btn';
            myBookmarksBtn.textContent = 'Bookmarks';
            myBookmarksBtn.onclick = () => this.showBookmarks();
            container.appendChild(myBookmarksBtn);

            // 创建记事本按钮
            const notepadBtn = document.createElement('button');
            notepadBtn.className = 'ya-btn ya-notepad-btn';
            notepadBtn.textContent = '记事本';
            notepadBtn.onclick = () => window.yaBookmarkManager.webdav.showNotepadDialog();
            container.appendChild(notepadBtn);

            // 在图集详情页添加收藏按钮
            if(location.pathname.includes('/a/')) {
                const bookmarkBtn = document.createElement('button');
                bookmarkBtn.className = 'ya-btn ya-bookmark-btn';
                bookmarkBtn.textContent = this.isBookmarked() ? '已收藏' : '收藏';
                bookmarkBtn.onclick = () => this.toggleBookmark();
                container.appendChild(bookmarkBtn);
            }

            // 使用MutationObserver监听DOM变化
            const observer = new MutationObserver((mutations) => {
                // 检查按钮是否还存在
                if (!document.querySelector('.ya-bookmark-container')) {
                    // 如果按钮不存在，重新添加到页面
                    if (document.body) {
                        if (document.body.firstChild) {
                            document.body.insertBefore(container, document.body.firstChild);
                        } else {
                            document.body.appendChild(container);
                        }
                    }
                }
            });

            // 开始观察整个文档
            observer.observe(document.documentElement, {
                childList: true,
                subtree: true
            });

            // 初始添加按钮
            if (document.body) {
                if (document.body.firstChild) {
                    document.body.insertBefore(container, document.body.firstChild);
                } else {
                    document.body.appendChild(container);
                }
            }

            // 添加样式
            GM_addStyle(`
                .ya-bookmark-container {
                    position: fixed;
                    top: 10px;
                    left: 10px;
                    z-index: 999999;
                    display: flex;
                    gap: 10px;
                }
                .ya-btn {
                    background: #f17c67;
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    transition: all 0.3s;
                    font-size: 14px;
                    z-index: 999999;
                }
                .ya-btn:hover {
                    background: #e76450;
                }
            `);
        }

        isBookmarked() {
            const currentUrl = location.href;
            return this.bookmarks.some(b => b.url === currentUrl);
        }

        toggleBookmark() {
            if(this.isBookmarked()) {
                this.removeBookmark();
            } else {
                this.addBookmark();
            }
        }

        addBookmark() {
            // 获取图集信息
            const title = document.querySelector('.tuji h1')?.textContent || document.title;
            const tujiContainer = document.querySelector('.tuji');

            // 精确提取模特和标签信息
            let model = '';
            let modelHTML = '';
            let tagList = [];
            let tagHTML = '';

            if (tujiContainer) {
                // 提取模特信息 - 查找包含"出镜模特："的段落
                const modelP = Array.from(tujiContainer.querySelectorAll('p')).find(p =>
                    p.textContent.includes('出镜模特：')
                );

                if (modelP) {
                    // 保存原始HTML，保留链接
                    modelHTML = modelP.innerHTML.replace('出镜模特：', '').trim();

                    // 将相对路径替换为绝对路径
                    const baseUrl = location.origin;
                    modelHTML = modelHTML.replace(/href="\/([^"]*)"/g, `href="${baseUrl}/$1" target="_blank"`);

                    // 同时保存纯文本版本
                    const modelLink = modelP.querySelector('a');
                    if (modelLink) {
                        model = modelLink.textContent.trim();
                    } else {
                        const modelText = modelP.textContent;
                        model = modelText.replace('出镜模特：', '').trim();
                    }
                }

                // 提取标签信息 - 查找包含"相关标签："的段落
                const tagP = Array.from(tujiContainer.querySelectorAll('p')).find(p =>
                    p.textContent.includes('相关标签：')
                );

                if (tagP) {
                    // 保存原始HTML，保留链接
                    tagHTML = tagP.innerHTML.replace('相关标签：', '').trim();

                    // 将相对路径替换为绝对路径
                    const baseUrl = location.origin;
                    tagHTML = tagHTML.replace(/href="\/([^"]*)"/g, `href="${baseUrl}/$1" target="_blank"`);

                    // 同时保存纯文本版本列表
                    const tagLinks = tagP.querySelectorAll('a');
                    if (tagLinks && tagLinks.length > 0) {
                        tagList = Array.from(tagLinks).map(tag => tag.textContent.trim());
                    }
                }
            }

            const thumbnail = document.querySelector('#kbox img')?.src || '';

            const bookmark = {
                id: Date.now().toString(),
                title: title,
                url: location.href,
                thumbnail: thumbnail,
                model: model,
                modelHTML: modelHTML,
                tagList: tagList,
                tagHTML: tagHTML,
                addTime: new Date().toISOString()
            };

            this.bookmarks.push(bookmark);
            this.saveBookmarks();

            // 更新按钮状态
            const bookmarkBtn = document.querySelector('.ya-bookmark-btn');
            if(bookmarkBtn) {
                bookmarkBtn.textContent = '已收藏';
                bookmarkBtn.classList.add('bookmarked');
            }

            GM_notification({
                text: '收藏成功！',
                timeout: 2000
            });
        }

        removeBookmark() {
            const currentUrl = location.href;
            this.bookmarks = this.bookmarks.filter(b => b.url !== currentUrl);
            this.saveBookmarks();

            // 更新按钮状态
            const bookmarkBtn = document.querySelector('.ya-bookmark-btn');
            if(bookmarkBtn) {
                bookmarkBtn.textContent = '收藏';
                bookmarkBtn.classList.remove('bookmarked');
            }

            GM_notification({
                text: '已取消收藏',
                timeout: 2000
            });
        }

        showBookmarks() {
            // 创建一个 DocumentFragment 来批量操作 DOM
            const fragment = document.createDocumentFragment();
            const bookmarkPage = document.createElement('div');
            bookmarkPage.className = 'ya-bookmark-page';

            // 获取当前列数并确保是数字类型
            const currentColumns = parseInt(GM_getValue('bookmark_columns', 6));

            // 分页相关参数
            const itemsPerPage = parseInt(GM_getValue('items_per_page', 30)); // 每页显示数量
            const currentPage = parseInt(GM_getValue('current_page', 1)); // 当前页码

            // 反转收藏顺序，最新的显示在前面
            const reversedBookmarks = [...this.bookmarks].sort((a, b) => new Date(b.addTime) - new Date(a.addTime));
            const totalItems = reversedBookmarks.length;
            const totalPages = Math.max(1, Math.ceil(totalItems / itemsPerPage));

            // 确保当前页码有效
            const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages);
            if (validCurrentPage !== currentPage) {
                GM_setValue('current_page', validCurrentPage);
            }

            // 创建头部
            const header = document.createElement('div');
            header.className = 'ya-bookmark-header';
            header.innerHTML = `
                <div class="ya-bookmark-controls">
                    <h1>我的收藏 (${this.bookmarks.length})</h1>
                    <div class="layout-control">
                        <label>每行显示:
                            <select id="column-selector">
                                ${[2,3,4,5,6,7,8,9,10,12,15,20].map(n =>
                                    `<option value="${n}" ${n === currentColumns ? 'selected' : ''}>${n}个</option>`
                                ).join('')}
                            </select>
                        </label>
                        <label style="margin-left: 15px;">每页显示:
                            <select id="page-size-selector">
                                ${[10,20,30,50,1000].map(n =>
                                    `<option value="${n}" ${n === itemsPerPage ? 'selected' : ''}>${n}个</option>`
                                ).join('')}
                            </select>
                        </label>
                    </div>
                    <button class="ya-btn clear-btn">清空收藏</button>
                </div>
                <div class="ya-bookmark-actions">
                    <div class="batch-open-control">
                        <select id="batch-size-selector">
                            <option value="10">10个</option>
                            <option value="20">20个</option>
                            <option value="30">30个</option>
                            <option value="-1">全部</option>
                        </select>
                        <button class="ya-btn batch-open-btn">批量打开</button>
                    </div>
                    <button class="ya-btn sync-settings-btn">同步设置</button>
                    <button class="ya-btn return-btn">返回</button>
                </div>
            `;

            // 创建网格容器
            const grid = document.createElement('div');
            grid.className = 'ya-bookmark-grid';

            // 确保立即应用列数
            requestAnimationFrame(() => {
                grid.style.setProperty('--columns', currentColumns);
            });

            // 计算当前页的数据范围
            const startIndex = (validCurrentPage - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
            const currentPageItems = reversedBookmarks.slice(startIndex, endIndex);

            // 存储待加载的收藏项及其对应的DOM元素
            const itemsToFetch = [];

            // 批量创建收藏项
            currentPageItems.forEach(bookmark => {
                const item = document.createElement('div');
                item.className = 'ya-bookmark-item';
                item.setAttribute('data-id', bookmark.id);
                item.setAttribute('data-url', bookmark.url);

                // 预加载图片并缓存
                const img = new Image();
                img.src = bookmark.thumbnail;
                img.loading = 'eager';  // 改为立即加载
                img.decoding = 'sync';  // 同步解码
                img.fetchPriority = 'high';  // 提高加载优先级

                // 创建初始信息区模特显示在这里
                const infoContent = `
                    <div class="cover">
                        <a href="${bookmark.url}" target="_blank">
                            <img src="${bookmark.thumbnail}"
                                 alt="${bookmark.title}"
                                 loading="eager"
                                 decoding="sync"
                                 fetchpriority="high"
                                 style="will-change: transform; contain: paint;">
                        </a>
                        <div class="item-actions">
                            <button class="action-btn view-btn" title="查看">👁</button>
                            <button class="action-btn delete-btn" data-bookmark-id="${bookmark.id}" title="删除">✕</button>
                        </div>
                        <div class="info">
                            <div class="bookmark-details">
                                ${bookmark.modelHTML ?
                                    `<div class="model">${bookmark.modelHTML}</div>` :
                                    (bookmark.model ?
                                        `<div class="model">${bookmark.model}</div>` :
                                        `<div class="model">加载中...</div>`
                                    )
                                }
                                ${bookmark.tagHTML ?
                                    `<div class="tags">${bookmark.tagHTML}</div>` :
                                    (bookmark.tagList && bookmark.tagList.length > 0 ?
                                        `<div class="tags">${bookmark.tagList.join(' ')}</div>` :
                                        `<div class="tags">加载中...</div>`
                                    )
                                }
                            </div>
                        </div>
                    </div>
                `;

                item.innerHTML = infoContent;
                grid.appendChild(item);

                // 如果收藏项缺少模特或标签信息，加入待获取列表
                if (!bookmark.model || !bookmark.tagList || bookmark.tagList.length === 0) {
                    itemsToFetch.push({
                        bookmark,
                        element: item
                    });
                }
            });

            // 组装页面
            bookmarkPage.appendChild(header);
            bookmarkPage.appendChild(grid);

            // 添加页面到文档片段
            fragment.appendChild(bookmarkPage);

            // 保存原始内容
            this.originalContent = document.body.innerHTML;

            // 一次性更新 DOM
            document.body.innerHTML = '';
            document.body.appendChild(fragment);

            // 绑定事件
            this.bindBookmarkEvents(bookmarkPage);

            // 创建分页导航
            const pagination = document.createElement('div');
            pagination.className = 'ya-pagination';

            // 生成分页HTML
            const paginationHTML = this.generatePaginationHTML(validCurrentPage, totalPages);
            pagination.innerHTML = paginationHTML;

            // 添加分页到页面
            bookmarkPage.appendChild(pagination);

            // 绑定分页事件
            this.bindPaginationEvents(bookmarkPage, totalPages);

            // 异步获取模特和标签信息
            if (itemsToFetch.length > 0) {
                this.fetchMissingInfos(itemsToFetch);
            }
        }

        // 添加新方法: 批量获取缺失的模特和标签信息
        async fetchMissingInfos(items) {
            // 最大同时请求数量
            const MAX_CONCURRENT_REQUESTS = 5;
            let activeRequests = 0;
            let queue = [...items];

            const processQueue = async () => {
                if (queue.length === 0 || activeRequests >= MAX_CONCURRENT_REQUESTS) return;

                const item = queue.shift();
                activeRequests++;

                try {
                    await this.fetchSingleItemInfo(item.bookmark, item.element);
                } catch (error) {
                    console.error(`获取信息失败: ${item.bookmark.url}`, error);
                    // 失败时显示提示
                    const modelEl = item.element.querySelector('.model');
                    const tagsEl = item.element.querySelector('.tags');
                    if (modelEl && !item.bookmark.model) modelEl.textContent = ' 获取失败';
                    if (tagsEl && (!item.bookmark.tagList || item.bookmark.tagList.length === 0)) tagsEl.textContent = ' 获取失败';
                } finally {
                    activeRequests--;
                    // 处理下一个
                    setTimeout(processQueue, 50);
                }
            };

            // 启动多个并行处理
            for (let i = 0; i < MAX_CONCURRENT_REQUESTS; i++) {
                processQueue();
            }
        }

        // 添加新方法: 获取单个收藏项的信息
        async fetchSingleItemInfo(bookmark, element) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: bookmark.url,
                    timeout: 10000,
                    onload: (response) => {
                        if (response.status === 200) {
                            try {
                                // 创建临时DOM解析页面内容
                                const parser = new DOMParser();
                                const doc = parser.parseFromString(response.responseText, 'text/html');

                                // 从URL中提取网站根域名
                                const urlObj = new URL(bookmark.url);
                                const baseUrl = urlObj.origin;

                                // 查找.tuji容器
                                const tujiContainer = doc.querySelector('.tuji');
                                if (tujiContainer) {
                                    // 提取模特信息
                                    const modelP = Array.from(tujiContainer.querySelectorAll('p')).find(p =>
                                        p.textContent.includes('出镜模特：')
                                    );

                                    let model = '';
                                    let modelHTML = '';
                                    if (modelP) {
                                        // 保存原始HTML，保留链接
                                        modelHTML = modelP.innerHTML.replace('出镜模特：', '').trim();

                                        // 将相对路径替换为绝对路径
                                        modelHTML = modelHTML.replace(/href="\/([^"]*)"/g, `href="${baseUrl}/$1" target="_blank"`);

                                        // 同时保存纯文本版本
                                        const modelLink = modelP.querySelector('a');
                                        if (modelLink) {
                                            model = modelLink.textContent.trim();
                                        } else {
                                            const modelText = modelP.textContent;
                                            model = modelText.replace('出镜模特：', '').trim();
                                        }
                                    }

                                    // 提取标签信息
                                    const tagP = Array.from(tujiContainer.querySelectorAll('p')).find(p =>
                                        p.textContent.includes('相关标签：')
                                    );

                                    let tagList = [];
                                    let tagHTML = '';
                                    if (tagP) {
                                        // 保存原始HTML，保留链接
                                        tagHTML = tagP.innerHTML.replace('相关标签：', '').trim();

                                        // 将相对路径替换为绝对路径
                                        tagHTML = tagHTML.replace(/href="\/([^"]*)"/g, `href="${baseUrl}/$1" target="_blank"`);

                                        // 同时保存纯文本版本列表
                                        const tagLinks = tagP.querySelectorAll('a');
                                        if (tagLinks && tagLinks.length > 0) {
                                            tagList = Array.from(tagLinks).map(tag => tag.textContent.trim());
                                        }
                                    }

                                    // 更新DOM元素
                                    const modelEl = element.querySelector('.model');
                                    const tagsEl = element.querySelector('.tags');

                                    if (modelEl && modelHTML) {
                                        modelEl.innerHTML = `${modelHTML}`;
                                        bookmark.model = model;
                                        bookmark.modelHTML = modelHTML;
                                    } else if (modelEl && model) {
                                        modelEl.textContent = `${model}`;
                                        bookmark.model = model;
                                    } else if (modelEl) {
                                        modelEl.textContent = '暂无信息';
                                    }

                                    if (tagsEl && tagHTML) {
                                        tagsEl.innerHTML = `${tagHTML}`;
                                        bookmark.tagList = tagList;
                                        bookmark.tagHTML = tagHTML;
                                    } else if (tagsEl && tagList.length > 0) {
                                        tagsEl.textContent = `${tagList.join(' ')}`;
                                        bookmark.tagList = tagList;
                                    } else if (tagsEl) {
                                        tagsEl.textContent = '暂无信息';
                                    }

                                    // 更新收藏数据
                                    this.saveBookmarks();

                                    resolve();
                                } else {
                                    reject(new Error('无法找到图集信息'));
                                }
                            } catch (error) {
                                reject(error);
                            }
                        } else {
                            reject(new Error(`请求失败: ${response.status}`));
                        }
                    },
                    onerror: (error) => reject(error),
                    ontimeout: () => reject(new Error('请求超时'))
                });
            });
        }

        // 生成分页HTML
        generatePaginationHTML(currentPage, totalPages) {
            // 确定要显示的页码范围
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            // 调整起始页，确保始终显示5个页码（如果有足够的页数）
            if (endPage - startPage < 4 && totalPages > 5) {
                startPage = Math.max(1, endPage - 4);
            }

            let pagesHTML = '';

            // 上一页按钮
            const prevDisabled = currentPage === 1 ? 'disabled' : '';
            pagesHTML += `<button class="ya-pagination-btn prev ${prevDisabled}" data-page="${currentPage - 1}" ${prevDisabled ? 'disabled' : ''}>上一页</button>`;

            // 页码按钮
            pagesHTML += '<div class="ya-pagination-pages">';

            // 第一页和省略号
            if (startPage > 1) {
                pagesHTML += `<button class="ya-pagination-btn" data-page="1">1</button>`;
                if (startPage > 2) {
                    pagesHTML += `<span class="ya-pagination-ellipsis">...</span>`;
                }
            }

            // 页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                pagesHTML += `<button class="ya-pagination-btn ${activeClass}" data-page="${i}">${i}</button>`;
            }

            // 最后一页和省略号
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pagesHTML += `<span class="ya-pagination-ellipsis">...</span>`;
                }
                pagesHTML += `<button class="ya-pagination-btn" data-page="${totalPages}">${totalPages}</button>`;
            }

            pagesHTML += '</div>';

            // 下一页按钮
            const nextDisabled = currentPage === totalPages ? 'disabled' : '';
            pagesHTML += `<button class="ya-pagination-btn next ${nextDisabled}" data-page="${currentPage + 1}" ${nextDisabled ? 'disabled' : ''}>下一页</button>`;

            // 跳转到指定页
            pagesHTML += `
                <div class="ya-pagination-jump">
                    跳至 <input type="number" min="1" max="${totalPages}" value="${currentPage}" class="ya-pagination-input"> 页
                    <button class="ya-pagination-btn jump-btn">确定跳转</button>
                </div>
            `;

            return pagesHTML;
        }

        // 将事件绑定抽离为单独的方法
        bindBookmarkEvents(bookmarkPage) {
            const columnSelector = bookmarkPage.querySelector('#column-selector');
            const pageSizeSelector = bookmarkPage.querySelector('#page-size-selector');
            const grid = bookmarkPage.querySelector('.ya-bookmark-grid');

            if (columnSelector && grid) {
                columnSelector.addEventListener('change', (e) => {
                    const newColumns = parseInt(e.target.value);
                    if (!isNaN(newColumns)) {
                        GM_setValue('bookmark_columns', newColumns);
                        requestAnimationFrame(() => {
                            grid.style.setProperty('--columns', newColumns);
                        });
                    }
                });
            }

            // 每页显示数量变化事件
            if (pageSizeSelector) {
                pageSizeSelector.addEventListener('change', (e) => {
                    const newPageSize = parseInt(e.target.value);
                    if (!isNaN(newPageSize)) {
                        GM_setValue('items_per_page', newPageSize);
                        GM_setValue('current_page', 1); // 重置到第一页
                        this.showBookmarks(); // 重新加载书签列表
                    }
                });
            }

            // 批量打开事件
            let currentBatchIndex = 0;
            const batchOpenBtn = bookmarkPage.querySelector('.batch-open-btn');
            const batchSizeSelector = bookmarkPage.querySelector('#batch-size-selector');

            if (batchOpenBtn && batchSizeSelector) {
                batchOpenBtn.onclick = () => {
                    const batchSize = parseInt(batchSizeSelector.value);
                    // 使用反转后的收藏列表
                    const reversedBookmarks = [...this.bookmarks].sort((a, b) => new Date(b.addTime) - new Date(a.addTime));
                    const totalItems = reversedBookmarks.length;

                    // 如果选择全部，则一次性打开所有
                    if (batchSize === -1) {
                        reversedBookmarks.forEach(bookmark => window.open(bookmark.url));
                        currentBatchIndex = 0;
                        return;
                    }

                    // 计算当前批次的起始和结束索引
                    const start = currentBatchIndex * batchSize;
                    const end = Math.min(start + batchSize, totalItems);

                    // 如果已经到达末尾，重新从头开始
                    if (start >= totalItems) {
                        currentBatchIndex = 0;
                        batchOpenBtn.textContent = '批量打开';
                        return;
                    }

                    // 打开当前批次的链接
                    for (let i = start; i < end; i++) {
                        window.open(reversedBookmarks[i].url);
                    }

                    // 更新批次索引
                    currentBatchIndex++;

                    // 更新按钮文本，显示进度
                    const remaining = totalItems - end;
                    if (remaining > 0) {
                        batchOpenBtn.textContent = `继续打开(剩余${remaining}个)`;
                    } else {
                        batchOpenBtn.textContent = '批量打开';
                        currentBatchIndex = 0;
                    }
                };
            }

            // 清空按钮事件
            const clearBtn = bookmarkPage.querySelector('.clear-btn');
            if (clearBtn) {
                clearBtn.onclick = () => {
                    // 清空收藏
                    this.bookmarks = [];
                    this.saveBookmarks();

                    // 清空显示的内容
                    const grid = document.querySelector('.ya-bookmark-grid');
                    if (grid) {
                        grid.innerHTML = '';
                    }

                    // 更新标题中的数量
                    const title = document.querySelector('.ya-bookmark-header h1');
                    if (title) {
                        title.textContent = '我的收藏 (0)';
                    }

                    // 显示通知
                    GM_notification({ text: '已清空所有收藏', timeout: 2000 });
                };
            }

            // 同步设置按钮事件
            const syncBtn = bookmarkPage.querySelector('.sync-settings-btn');
            if (syncBtn) {
                syncBtn.onclick = () => this.webdav.showConfigDialog();
            }

            // 返回按钮事件
            const returnBtn = bookmarkPage.querySelector('.return-btn');
            if (returnBtn) {
                returnBtn.onclick = () => this.restoreOriginalPage();
            }

            // 删除按钮事件
            bookmarkPage.querySelectorAll('.delete-btn').forEach(btn => {
                btn.onclick = (e) => {
                    e.stopPropagation();
                    const id = e.target.getAttribute('data-bookmark-id');
                    this.removeBookmarkById(id);
                };
            });
        }

        // 添加恢复原页面的方法
        restoreOriginalPage() {
            if (this.originalContent) {
                document.body.innerHTML = this.originalContent;
                // 重新初始化UI
                this.initUI();
            }
        }

        // 修改删除书签的方法
        removeBookmarkById(id) {
            this.bookmarks = this.bookmarks.filter(b => b.id !== id);
            this.saveBookmarks();

            // 从页面中移除对应的元素，添加淡出动画
            const item = document.querySelector(`[data-id="${id}"]`);
            if (item) {
                item.style.transition = 'opacity 0.3s';
                item.style.opacity = '0';
                setTimeout(() => item.remove(), 300);
            }

            GM_notification({
                text: '已删除收藏',
                timeout: 2000
            });

            // 检查当前页是否已经没有内容，如果是则返回上一页
            const grid = document.querySelector('.ya-bookmark-grid');
            if (grid && grid.children.length === 1) { // 只剩一个元素被删除
                const currentPage = parseInt(GM_getValue('current_page', 1));
                const totalPages = Math.max(1, Math.ceil((this.bookmarks.length - 1) / parseInt(GM_getValue('items_per_page', 30))));

                // 如果删除后总页数减少且当前页大于总页数，则返回最后一页
                if (currentPage > totalPages) {
                    GM_setValue('current_page', totalPages);
                    this.showBookmarks(); // 重新加载书签列表
                } else if (currentPage > 1 && grid.children.length === 1) {
                    // 如果当前页已经没有内容且不是第一页，则返回上一页
                    GM_setValue('current_page', currentPage - 1);
                    this.showBookmarks(); // 重新加载书签列表
                } else {
                    // 更新标题中的数量
                    const title = document.querySelector('.ya-bookmark-header h1');
                    if (title) {
                        title.textContent = `我的收藏 (${this.bookmarks.length})`;
                    }
                }
            }
        }

        // 绑定分页事件
        bindPaginationEvents(bookmarkPage, totalPages) {
            // 页码按钮点击事件
            bookmarkPage.querySelectorAll('.ya-pagination-btn[data-page]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    if (btn.classList.contains('disabled')) return;

                    const page = parseInt(btn.getAttribute('data-page'));
                    if (!isNaN(page) && page >= 1 && page <= totalPages) {
                        GM_setValue('current_page', page);
                        this.showBookmarks(); // 重新加载书签列表
                        // 滚动到页面顶部
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    }
                });
            });

            // 跳转按钮点击事件
            const jumpBtn = bookmarkPage.querySelector('.jump-btn');
            const pageInput = bookmarkPage.querySelector('.ya-pagination-input');

            if (jumpBtn && pageInput) {
                // 输入框回车事件
                pageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        jumpBtn.click();
                    }
                });

                // 新增：输入框内容变化立即跳转
                pageInput.addEventListener('input', (e) => {
                    const page = parseInt(e.target.value);
                    if (!isNaN(page) && page >= 1 && page <= totalPages) {
                        // 延迟300ms执行，防止用户连续输入的情况
                        clearTimeout(this.jumpTimeout);
                        this.jumpTimeout = setTimeout(() => {
                            GM_setValue('current_page', page);
                            this.showBookmarks(); // 重新加载书签列表
                            // 滚动到页面顶部
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                        }, 300);
                    }
                });

                // 跳转按钮点击事件
                jumpBtn.addEventListener('click', () => {
                    const page = parseInt(pageInput.value);
                    if (!isNaN(page) && page >= 1 && page <= totalPages) {
                        GM_setValue('current_page', page);
                        this.showBookmarks(); // 重新加载书签列表
                        // 滚动到页面顶部
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    } else {
                        // 输入无效页码，重置为当前页
                        pageInput.value = GM_getValue('current_page', 1);
                    }
                });
            }
        }

        async autoSync() {
            // 检查是否启用自动同步
            if (!this.webdav.config.server || !this.webdav.AUTO_SYNC) return;

            try {
                const mergedBookmarks = await this.webdav.sync(this.bookmarks);
                if (mergedBookmarks.length !== this.bookmarks.length) {
                    this.bookmarks = mergedBookmarks;
                    this.saveBookmarks();
                    GM_notification({ text: '收藏已自动同步', timeout: 2000 });
                }
            } catch (error) {
                console.error('自动同步失败:', error);
            }
        }

        async manualSync() {
            if (!this.webdav.config.server) {
                GM_notification({ text: '请先配置同步设置', timeout: 2000 });
                this.webdav.showConfigDialog();
                return;
            }

            try {
                GM_notification({ text: '开始同步...', timeout: 2000 });
                const mergedBookmarks = await this.webdav.sync(this.bookmarks);
                this.bookmarks = mergedBookmarks;
                this.saveBookmarks();

                // 添加同步日志
                this.webdav.addSyncLog('success', `手动同步成功，共${mergedBookmarks.length}个收藏`);

                // 如果当前正在显示收藏列表，则刷新显示
                const bookmarkList = document.querySelector('.ya-bookmark-list');
                if (bookmarkList) {
                    bookmarkList.remove();
                    document.querySelector('.ya-mask')?.remove();
                    this.showBookmarks();
                }

                GM_notification({ text: '同步完成！', timeout: 2000 });
            } catch (error) {
                console.error('手动同步失败:', error);
                this.webdav.addSyncLog('error', `手动同步失败: ${error.message}`);
                GM_notification({ text: '同步失败: ' + error.message, timeout: 3000 });
            }
        }
    }

    // 直接初始化，不等待页面加载完成
    window.yaBookmarkManager = new YaBookmarkManager();
})();