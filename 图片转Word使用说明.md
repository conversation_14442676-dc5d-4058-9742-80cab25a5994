# 图片批量插入Word工具

## 功能说明
这个Python工具可以：
1. 弹出对话框选择包含图片的文件夹
2. 自动识别图片的横竖方向
3. 将图片按类型分组插入Word文档：
   - **横向图片**：2张一页，上下排列
   - **竖向图片**：2张一页，左右排列
4. 图片在页面中上下居中，左右平均占满
5. Word文档命名为文件夹名

## 安装依赖

### 方法1：使用批处理文件（推荐）
双击运行 `install_requirements.bat`

### 方法2：手动安装
```bash
pip install python-docx Pillow
```

## 使用方法

1. 确保已安装Python 3.6+
2. 安装依赖包
3. 运行脚本：
   ```bash
   python images_to_word.py
   ```
4. 在弹出的对话框中选择包含图片的文件夹
5. 等待处理完成，Word文档会保存在选择的文件夹中

## 支持的图片格式
- JPG/JPEG
- PNG
- BMP
- GIF
- TIFF
- WebP

## 布局说明

### 横向图片（宽>高）
- 2张图片上下排列在一页
- 每张图片占满页面宽度
- 上下居中显示

### 竖向图片（高>宽）
- 2张图片左右并排在一页
- 每张图片占页面一半宽度
- 左右居中显示

### 单张图片
- 如果某个方向只有1张图片，会单独占一页
- 居中显示

## 页面设置
- 纸张：A4
- 边距：上下左右各1cm
- 图片自动调整大小以适应页面

## 注意事项
1. 确保选择的文件夹中包含图片文件
2. 图片文件名不要包含特殊字符
3. 处理大量图片时可能需要等待一段时间
4. 生成的Word文档会保存在原图片文件夹中

## 错误处理
- 如果图片无法读取，会跳过该图片并继续处理
- 如果文件夹中没有图片，会显示警告信息
- 处理过程中的错误会显示详细信息

## 示例
假设您有一个名为"旅行照片"的文件夹，包含：
- 3张横向风景照
- 4张竖向人物照

运行脚本后会生成"旅行照片.docx"，包含：
- 第1页：2张横向风景照（上下排列）
- 第2页：1张横向风景照（居中）
- 第3页：2张竖向人物照（左右排列）
- 第4页：2张竖向人物照（左右排列）
