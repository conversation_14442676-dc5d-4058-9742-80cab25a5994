#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片批量插入Word文档工具
功能：选择文件夹，将图片按横竖分类插入Word，2张一页
"""

import os
import tkinter as tk
from tkinter import filedialog, messagebox
from docx import Document
from docx.shared import Inches, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.section import WD_SECTION
from PIL import Image
import sys

class ImageToWordConverter:
    def __init__(self):
        self.supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp')
        
    def select_folder(self):
        """弹出对话框选择文件夹"""
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        folder_path = filedialog.askdirectory(
            title="选择包含图片的文件夹",
            initialdir=os.getcwd()
        )
        
        root.destroy()
        return folder_path
    
    def get_image_orientation(self, image_path):
        """判断图片是横向还是竖向"""
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                return "horizontal" if width > height else "vertical"
        except Exception as e:
            print(f"无法读取图片 {image_path}: {e}")
            return "horizontal"  # 默认横向
    
    def get_images_from_folder(self, folder_path):
        """从文件夹获取所有图片文件"""
        images = []
        
        if not os.path.exists(folder_path):
            return images
            
        for filename in os.listdir(folder_path):
            if filename.lower().endswith(self.supported_formats):
                image_path = os.path.join(folder_path, filename)
                orientation = self.get_image_orientation(image_path)
                images.append({
                    'path': image_path,
                    'name': filename,
                    'orientation': orientation
                })
        
        return images
    
    def classify_images(self, images):
        """按横竖分类图片"""
        horizontal_images = [img for img in images if img['orientation'] == 'horizontal']
        vertical_images = [img for img in images if img['orientation'] == 'vertical']
        
        return horizontal_images, vertical_images
    
    def add_image_pair_to_page(self, doc, image1_path, image2_path=None, orientation="horizontal"):
        """在一页中添加1-2张图片"""
        # 添加新页面（除了第一页）
        if len(doc.paragraphs) > 0:
            doc.add_page_break()
        
        # 设置页面边距
        section = doc.sections[-1]
        section.top_margin = Cm(1)
        section.bottom_margin = Cm(1)
        section.left_margin = Cm(1)
        section.right_margin = Cm(1)
        
        if orientation == "horizontal":
            # 横向图片：上下排列
            self.add_horizontal_images(doc, image1_path, image2_path)
        else:
            # 竖向图片：左右排列
            self.add_vertical_images(doc, image1_path, image2_path)
    
    def add_horizontal_images(self, doc, image1_path, image2_path=None):
        """添加横向图片（上下排列）"""
        # 计算图片尺寸（页面宽度减去边距）
        page_width = Cm(21 - 2)  # A4宽度减去左右边距
        image_height = Cm(8) if image2_path else Cm(16)  # 如果有两张图片，每张高8cm，否则16cm
        
        # 第一张图片
        p1 = doc.add_paragraph()
        p1.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run1 = p1.add_run()
        run1.add_picture(image1_path, width=page_width, height=image_height)
        
        if image2_path:
            # 添加一点间距
            doc.add_paragraph()
            
            # 第二张图片
            p2 = doc.add_paragraph()
            p2.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run2 = p2.add_run()
            run2.add_picture(image2_path, width=page_width, height=image_height)
    
    def add_vertical_images(self, doc, image1_path, image2_path=None):
        """添加竖向图片（左右排列）"""
        if image2_path:
            # 两张竖向图片并排
            image_width = Cm(9)  # 每张图片宽度
            image_height = Cm(16)  # 图片高度
            
            # 创建表格来实现左右布局
            table = doc.add_table(rows=1, cols=2)
            table.autofit = False
            
            # 设置列宽
            for col in table.columns:
                col.width = Cm(9.5)
            
            # 左侧图片
            cell1 = table.cell(0, 0)
            p1 = cell1.paragraphs[0]
            p1.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run1 = p1.add_run()
            run1.add_picture(image1_path, width=image_width, height=image_height)
            
            # 右侧图片
            cell2 = table.cell(0, 1)
            p2 = cell2.paragraphs[0]
            p2.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run2 = p2.add_run()
            run2.add_picture(image2_path, width=image_width, height=image_height)
            
        else:
            # 单张竖向图片居中
            page_height = Cm(29.7 - 2)  # A4高度减去上下边距
            image_width = Cm(12)
            
            p = doc.add_paragraph()
            p.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = p.add_run()
            run.add_picture(image1_path, width=image_width)
    
    def create_word_document(self, folder_path):
        """创建Word文档"""
        # 获取文件夹名作为文档名
        folder_name = os.path.basename(folder_path)
        
        # 获取图片列表
        images = self.get_images_from_folder(folder_path)
        
        if not images:
            messagebox.showwarning("警告", "选择的文件夹中没有找到支持的图片文件！")
            return
        
        print(f"找到 {len(images)} 张图片")
        
        # 分类图片
        horizontal_images, vertical_images = self.classify_images(images)
        
        print(f"横向图片: {len(horizontal_images)} 张")
        print(f"竖向图片: {len(vertical_images)} 张")
        
        # 创建Word文档
        doc = Document()
        
        # 处理横向图片（2张一页，上下排列）
        for i in range(0, len(horizontal_images), 2):
            image1 = horizontal_images[i]['path']
            image2 = horizontal_images[i + 1]['path'] if i + 1 < len(horizontal_images) else None
            
            print(f"添加横向图片页面: {os.path.basename(image1)}" + 
                  (f" 和 {os.path.basename(image2)}" if image2 else ""))
            
            self.add_image_pair_to_page(doc, image1, image2, "horizontal")
        
        # 处理竖向图片（2张一页，左右排列）
        for i in range(0, len(vertical_images), 2):
            image1 = vertical_images[i]['path']
            image2 = vertical_images[i + 1]['path'] if i + 1 < len(vertical_images) else None
            
            print(f"添加竖向图片页面: {os.path.basename(image1)}" + 
                  (f" 和 {os.path.basename(image2)}" if image2 else ""))
            
            self.add_image_pair_to_page(doc, image1, image2, "vertical")
        
        # 保存文档
        output_path = os.path.join(folder_path, f"{folder_name}.docx")
        doc.save(output_path)
        
        messagebox.showinfo("完成", f"Word文档已创建：\n{output_path}")
        print(f"文档已保存到: {output_path}")
    
    def run(self):
        """运行主程序"""
        print("图片批量插入Word工具")
        print("=" * 50)
        
        # 选择文件夹
        folder_path = self.select_folder()
        
        if not folder_path:
            print("未选择文件夹，程序退出")
            return
        
        print(f"选择的文件夹: {folder_path}")
        
        try:
            # 创建Word文档
            self.create_word_document(folder_path)
            
        except Exception as e:
            error_msg = f"处理过程中出现错误: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)

def main():
    """主函数"""
    try:
        converter = ImageToWordConverter()
        converter.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        messagebox.showerror("错误", f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
